# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**GameVault** is a modern React-based game collection web application built with TypeScript and Vite. It allows users to manage their personal game libraries with features like game search, collection organization, and analytics.

### Tech Stack
- **Frontend**: React 18.3.1, TypeScript 5.5.3, Vite 5.4.8
- **Styling**: Tailwind CSS 3.4.13 with ShadCN UI components
- **State Management**: Zustand 5.0.6 for client state, TanStack Query 5.81.5 for server state
- **Backend**: Supabase (PostgreSQL, authentication, storage)
- **APIs**: IGDB API (via Twitch), YouTube Data API v3

## Essential Commands

### Development
```bash
npm run dev           # Start development server (http://localhost:5173)
npm run build         # Build for production (TypeScript check + Vite build)
npm run preview       # Preview production build
npm run lint          # Run ESLint
```

### Environment Setup
Create `.env` file with:
```
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_IGDB_CLIENT_ID=your_igdb_client_id
VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
VITE_YOUTUBE_API_KEY=your_youtube_api_key
```

## Architecture

### Database Schema
The application uses PostgreSQL via Supabase with three main tables:
- `games`: Core game data (title, platform, genres, metadata)
- `user_games`: User-specific game data (status, rating, notes, hours)
- `price_tracking`: Price monitoring across stores

### State Management
- **Authentication**: `useAuthStore` (src/stores/useAuthStore.ts)
- **Game Data**: `useGameStore` (src/stores/useGameStore.ts)
- **Server State**: TanStack Query for API calls and caching

### Key Services
- **Supabase Client**: `src/lib/supabase.ts` - Database operations, auth, storage
- **API Service**: `src/lib/api.ts` - IGDB and YouTube API integrations
- **IGDB Proxy**: `supabase/functions/igdb-proxy/index.ts` - Edge function for IGDB API calls

### Component Structure
```
src/components/
├── games/          # Game-specific components (GameCard, GameFilters, GameSearch)
├── layout/         # Layout components (Header, Layout, Sidebar)
└── ui/             # ShadCN UI components (reusable)
```

### Pages
- `Dashboard.tsx`: Main dashboard with collection analytics
- `Collection.tsx`: Game collection management
- `AddGame.tsx`: Add new games via IGDB search

### Type Definitions
Core types defined in `src/types/index.ts`:
- `Game`, `UserGame`, `PriceTracking`
- `Platform` (PC, Xbox 360), `GameStatus`, `Genre`
- `CollectionStats` for analytics

## Important Implementation Details

### Path Alias
- Use `@/` prefix for imports (configured in vite.config.ts and tsconfig.json)
- Example: `import { Button } from '@/components/ui/button'`

### API Integration
- IGDB API calls go through Supabase Edge Function for CORS and authentication
- YouTube API calls are direct from client
- All API credentials are environment variables

### Platform Support
Supports all major gaming platforms including:
- **PC/Mac**: PC, Mac, Steam Deck
- **PlayStation**: PS5, PS4, PS3, PS2, PS1
- **Xbox**: Xbox Series X/S, Xbox One, Xbox 360, Xbox
- **Nintendo**: Switch, 3DS, DS, Wii U, Wii, GameCube
- **Mobile**: iOS, Android

### Authentication Flow
- Supabase Auth with email/password
- Row Level Security enabled on all tables
- User can only access their own user_games entries

### Storage
- Supabase Storage buckets: `game-covers`, `game-screenshots`
- Helper functions in `src/lib/supabase.ts`

## Development Workflow

1. **Adding New Features**: Follow existing patterns in components and stores
2. **Database Changes**: Update migration files in `supabase/migrations/`
3. **API Changes**: Modify services in `src/lib/` and update types
4. **UI Components**: Use ShadCN components from `src/components/ui/`
5. **Testing**: Run `npm run lint` and `npm run build` before committing

## Key Conventions

- Use TypeScript strictly - all files are .ts/.tsx
- Follow React hooks patterns for state management
- Implement proper error handling in API calls
- Use Tailwind classes for styling
- Maintain responsive design principles
- Follow existing naming conventions for components and functions