# GameVault: Modern Game Collection Web App

<div align="center">
  <img src="https://images.pexels.com/photos/442576/pexels-photo-442576.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" alt="GameVault Banner" width="100%" height="300" style="object-fit: cover; border-radius: 8px;">
  
  <h3>🎮 Your Personal Game Library Manager</h3>
  <p>A sleek and modern web application designed to help gamers manage their personal game collections with style and efficiency.</p>
  
  [![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue.svg)](https://www.typescriptlang.org/)
  [![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.13-38B2AC.svg)](https://tailwindcss.com/)
  [![Supabase](https://img.shields.io/badge/Supabase-Latest-green.svg)](https://supabase.com/)
  [![Vite](https://img.shields.io/badge/Vite-5.4.8-646CFF.svg)](https://vitejs.dev/)
</div>

---

## ✨ Features

### 🎯 Core Functionality
- **📚 Personalized Game Collection**: Organize your games by status (Playing, Completed, Backlog, Wishlist)
- **🔍 Smart Game Search**: Automatically fetch game details, cover art, screenshots, and YouTube links using IGDB API
- **📊 Collection Analytics**: View insightful statistics about your game library with beautiful charts
- **🎨 Advanced Filtering**: Filter by platform, genre, status, and rating with real-time search
- **📱 Responsive Design**: Seamless experience across desktop, tablet, and mobile devices

### 🚀 Modern Tech Stack
- **⚡ Lightning Fast**: Built with Vite for instant development and optimized builds
- **🎭 Beautiful UI**: Crafted with Tailwind CSS and ShadCN UI components
- **🔐 Secure Authentication**: Powered by Supabase with Row Level Security
- **📡 Real-time Updates**: Live data synchronization across devices
- **🌙 Dark Mode**: Elegant theme switching with system preference detection

### 🎮 Gaming Features
- **🎯 IGDB Integration**: Rich game data from the Internet Game Database
- **📺 YouTube Integration**: Automatic trailer and gameplay video discovery
- **📸 Media Management**: Upload custom cover images and screenshots
- **⭐ Personal Ratings**: Rate games and add personal notes
- **⏱️ Time Tracking**: Track hours played for each game
- **💰 Price Tracking**: Monitor game prices across different stores (coming soon)

---

## 🚀 Quick Start

### Prerequisites

Before you begin, ensure you have the following installed:
- **Node.js** (v18 or higher) - [Download here](https://nodejs.org/)
- **npm** or **yarn** package manager
- **Git** for version control

### 1. 📥 Clone the Repository

```bash
git clone <repository-url>
cd gamevault
```

### 2. 📦 Install Dependencies

```bash
npm install
# or
yarn install
```

### 3. 🔧 Environment Setup

Create a `.env` file in the root directory:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# API Keys
VITE_IGDB_CLIENT_ID=your_igdb_client_id
VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
VITE_YOUTUBE_API_KEY=your_youtube_api_key
```

### 4. 🗄️ Database Setup

#### Supabase Configuration

1. **Create a Supabase Project**: Visit [Supabase](https://supabase.com/) and create a new project
2. **Get your credentials**: Find your project URL and anon key in Project Settings → API
3. **Run the migrations**: Execute the SQL migration files in your Supabase SQL Editor

For detailed database setup instructions, see [DATABASE_SETUP.md](DATABASE_SETUP.md).

#### Database Schema

The project uses the following main tables:

**Core Tables:**
- **`games`**: Stores game information (title, platform, genres, cover art, etc.)
- **`user_games`**: Links users to their games with personal data (status, rating, notes)
- **`price_tracking`**: Tracks game prices across different stores

**User Management Tables:**
- **`user_profiles`**: Extended user information (username, display name, avatar)
- **`user_preferences`**: User settings and application preferences
- **`user_collection_stats`**: Aggregated collection statistics view

#### Storage Buckets

The following storage buckets are automatically created:
- `game-covers`: For game cover images
- `game-screenshots`: For game screenshots
- `user-avatars`: For user profile pictures

### 5. 🎮 API Keys Setup

#### IGDB API (Twitch)
1. Visit [Twitch Developer Console](https://dev.twitch.tv/console/apps)
2. Create a new application
3. Copy your Client ID and Client Secret

#### YouTube Data API
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API Key)

### 6. 🚀 Launch the Application

```bash
npm run dev
# or
yarn dev
```

Visit `http://localhost:5173` to see your application running! 🎉

---

## 🏗️ Project Structure

```
gamevault/
├── 📁 public/                 # Static assets
├── 📁 src/
│   ├── 📁 components/         # Reusable UI components
│   │   ├── 📁 games/         # Game-specific components
│   │   ├── 📁 layout/        # Layout components
│   │   └── 📁 ui/            # ShadCN UI components
│   ├── 📁 hooks/             # Custom React hooks
│   ├── 📁 lib/               # Utility functions and API services
│   ├── 📁 pages/             # Main page components
│   ├── 📁 stores/            # Zustand state management
│   ├── 📁 types/             # TypeScript type definitions
│   └── 📄 main.tsx           # Application entry point
├── 📁 supabase/
│   └── 📁 migrations/        # Database migration files
├── 📄 package.json           # Dependencies and scripts
├── 📄 tailwind.config.js     # Tailwind CSS configuration
├── 📄 vite.config.ts         # Vite build configuration
└── 📄 README.md              # This file
```

---

## 🎨 Design Philosophy

GameVault follows modern design principles to create an exceptional user experience:

### 🎯 Design Principles
- **Minimalist Elegance**: Clean, uncluttered interface that focuses on content
- **Consistent Visual Language**: Unified color scheme, typography, and spacing
- **Intuitive Navigation**: Logical information architecture and user flows
- **Responsive First**: Mobile-first design that scales beautifully
- **Accessibility**: WCAG compliant with proper contrast ratios and keyboard navigation

### 🎨 Visual Elements
- **Color System**: Carefully crafted color palette with semantic meaning
- **Typography**: Modern font stack with proper hierarchy
- **Animations**: Subtle micro-interactions using Framer Motion
- **Icons**: Consistent iconography from Lucide React
- **Spacing**: 8px grid system for perfect alignment

---

## 🛠️ Technology Stack

### Frontend
| Technology | Version | Purpose |
|------------|---------|---------|
| **React** | 18.3.1 | UI framework with hooks and modern patterns |
| **TypeScript** | 5.5.3 | Type safety and better developer experience |
| **Vite** | 5.4.8 | Fast build tool and development server |
| **Tailwind CSS** | 3.4.13 | Utility-first CSS framework |
| **ShadCN UI** | Latest | Beautiful, accessible component library |
| **Framer Motion** | 12.23.0 | Smooth animations and transitions |
| **Zustand** | 5.0.6 | Lightweight state management |
| **React Hook Form** | 7.60.0 | Performant form handling |
| **React Query** | 5.81.5 | Server state management |

### Backend & Database
| Technology | Purpose |
|------------|---------|
| **Supabase** | PostgreSQL database, authentication, and storage |
| **Row Level Security** | Fine-grained access control |
| **Real-time subscriptions** | Live data updates |

### APIs & Integrations
| Service | Purpose |
|---------|---------|
| **IGDB API** | Game database and metadata |
| **YouTube Data API** | Game trailers and videos |
| **Twitch OAuth** | IGDB authentication |

---

## 📊 Database Schema

### Games Table
```sql
CREATE TABLE games (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  platform TEXT NOT NULL CHECK (platform IN ('PC', 'Xbox 360')),
  genres TEXT[] DEFAULT '{}',
  developer TEXT,
  publisher TEXT,
  release_date DATE,
  description TEXT,
  cover_image TEXT,
  screenshots TEXT[] DEFAULT '{}',
  youtube_links TEXT[] DEFAULT '{}',
  metacritic_score INTEGER CHECK (metacritic_score >= 0 AND metacritic_score <= 100),
  igdb_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### User Games Table
```sql
CREATE TABLE user_games (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'backlog' CHECK (status IN ('playing', 'completed', 'backlog', 'wishlist')),
  personal_rating INTEGER CHECK (personal_rating >= 1 AND personal_rating <= 5),
  personal_notes TEXT,
  hours_played NUMERIC CHECK (hours_played >= 0),
  date_added TIMESTAMPTZ DEFAULT NOW(),
  date_completed TIMESTAMPTZ,
  is_wishlist BOOLEAN DEFAULT FALSE,
  UNIQUE(user_id, game_id)
);
```

### Price Tracking Table
```sql
CREATE TABLE price_tracking (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id UUID NOT NULL REFERENCES games(id) ON DELETE CASCADE,
  shop_name TEXT NOT NULL,
  shop_url TEXT NOT NULL,
  price NUMERIC NOT NULL CHECK (price >= 0),
  currency TEXT NOT NULL DEFAULT 'EUR',
  last_updated TIMESTAMPTZ DEFAULT NOW()
);
```

---

## 🔮 Future Enhancements

### 🎯 Core Features
- [ ] **Authentication Pages**: Beautiful login/register/password reset flows
- [ ] **Landing Page**: Compelling public-facing homepage
- [ ] **Game Detail Pages**: Rich individual game pages with full media galleries
- [ ] **Advanced Collection Management**: Bulk operations and advanced filtering
- [ ] **Price Tracking Dashboard**: Historical price data and alerts
- [ ] **Recommendation Engine**: AI-powered game suggestions

### 🎨 UI/UX Improvements
- [ ] **Enhanced Animations**: More sophisticated micro-interactions
- [ ] **Loading States**: Beautiful skeleton screens and loading animations
- [ ] **Error Boundaries**: Graceful error handling with recovery options
- [ ] **Accessibility**: Full WCAG 2.1 AA compliance
- [ ] **PWA Features**: Offline support and mobile app-like experience

### 🚀 Technical Enhancements
- [ ] **Performance Optimization**: Code splitting and lazy loading
- [ ] **Testing Suite**: Comprehensive unit and integration tests
- [ ] **CI/CD Pipeline**: Automated testing and deployment
- [ ] **Monitoring**: Error tracking and performance monitoring
- [ ] **SEO Optimization**: Meta tags and structured data

### 🎮 Gaming Features
- [ ] **Social Features**: Share collections and reviews with friends
- [ ] **Achievement System**: Gamify the collection experience
- [ ] **Import/Export**: Backup and migrate collections
- [ ] **Platform Integration**: Steam, Epic Games, Xbox Live integration
- [ ] **Advanced Analytics**: Detailed gaming statistics and insights

---

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### 🐛 Bug Reports
- Use the issue tracker to report bugs
- Include steps to reproduce the issue
- Provide screenshots if applicable

### 💡 Feature Requests
- Describe the feature and its benefits
- Provide mockups or examples if possible
- Discuss implementation approaches

### 🔧 Development
1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### 📝 Code Style
- Follow the existing code style
- Use TypeScript for type safety
- Write meaningful commit messages
- Add comments for complex logic

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 Acknowledgments

- **[IGDB](https://www.igdb.com/)** - For providing comprehensive game data
- **[Supabase](https://supabase.com/)** - For the amazing backend-as-a-service platform
- **[ShadCN UI](https://ui.shadcn.com/)** - For the beautiful component library
- **[Tailwind CSS](https://tailwindcss.com/)** - For the utility-first CSS framework
- **[Lucide](https://lucide.dev/)** - For the consistent icon library

---

## 📞 Support

If you encounter any issues or have questions:

1. **Check the documentation** in this README
2. **Search existing issues** in the GitHub repository
3. **Create a new issue** with detailed information
4. **Join our community** discussions

---

<div align="center">
  <h3>🎮 Happy Gaming! 🎮</h3>
  <p>Built with ❤️ for the gaming community</p>
  
  <img src="https://images.pexels.com/photos/3165335/pexels-photo-3165335.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1" alt="Gaming Setup" width="100%" height="200" style="object-fit: cover; border-radius: 8px; margin-top: 20px;">
</div>