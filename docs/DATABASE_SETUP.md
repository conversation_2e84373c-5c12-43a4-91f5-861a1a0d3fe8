# GameVault Database Setup

This document explains how to set up the database for GameVault using Supabase.

## Prerequisites

1. **Supabase Account**: Create an account at [supabase.com](https://supabase.com)
2. **Supabase CLI**: Install the Supabase CLI for running migrations
   ```bash
   npm install -g supabase
   ```

## Database Schema Overview

GameVault uses the following main tables:

### Core Tables
- **`games`**: Stores game information (title, platform, genres, metadata)
- **`user_games`**: Junction table linking users to their games with status and personal data
- **`price_tracking`**: Tracks game prices across different stores

### User Management Tables
- **`user_profiles`**: Extended user information (username, display name, avatar)
- **`user_preferences`**: User settings and preferences
- **`user_collection_stats`**: View for aggregated collection statistics

### Storage Buckets
- **`game-covers`**: Game cover images
- **`game-screenshots`**: Game screenshots  
- **`user-avatars`**: User profile pictures

## Setup Instructions

### 1. Create a New Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully provisioned
3. Note down your project URL and anon key from the API settings

### 2. Configure Environment Variables

Create a `.env` file in your project root:

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_IGDB_CLIENT_ID=your_igdb_client_id
VITE_IGDB_CLIENT_SECRET=your_igdb_client_secret
VITE_YOUTUBE_API_KEY=your_youtube_api_key
```

### 3. Initialize Supabase Locally (Optional)

If you want to use local development:

```bash
supabase login
supabase init
supabase link --project-ref your-project-ref
```

### 4. Run Database Migrations

#### Option A: Using Supabase CLI (Recommended)

```bash
supabase db push
```

#### Option B: Manual SQL Execution

If you prefer to run the migrations manually:

1. Go to your Supabase dashboard → SQL Editor
2. Run the migrations in order:
   - `20250708083705_muddy_bird.sql` (Core schema)
   - `20250708120000_expand_platforms.sql` (Platform expansion)
   - `20250708150000_add_user_features.sql` (User features)

### 5. Verify Setup

After running the migrations, verify that:

1. **Tables are created**: Check the Table Editor in your Supabase dashboard
2. **RLS is enabled**: All tables should have Row Level Security enabled
3. **Storage buckets exist**: Check the Storage section for the three buckets
4. **Policies are active**: Verify that policies are created for each table

## Features Enabled

### Authentication
- Email/password authentication
- Automatic user profile creation on signup
- Password reset functionality

### User Management
- User profiles with usernames and display names
- User preferences and settings
- Avatar upload support

### Game Collection
- Personal game libraries
- Game status tracking (playing, completed, backlog, wishlist)
- Personal ratings and notes
- Hours played tracking

### Security
- Row Level Security (RLS) on all tables
- Users can only access their own data
- Secure file uploads with proper permissions

## Database Functions

### Automatic Profile Creation
When a user signs up, the `handle_new_user()` function automatically creates:
- A user profile entry
- Default user preferences
- Proper initial settings

### Collection Statistics
The `user_collection_stats` view provides aggregated statistics:
- Total games count
- Completed games count
- Currently playing count
- Wishlist count
- Total hours played
- Average rating

## Troubleshooting

### Common Issues

1. **Migration Errors**: 
   - Ensure you're running migrations in the correct order
   - Check that your Supabase project is fully provisioned

2. **Permission Errors**:
   - Verify that RLS policies are correctly applied
   - Check that users are properly authenticated

3. **Storage Issues**:
   - Ensure storage buckets are created and public
   - Verify storage policies allow proper access

### Getting Help

If you encounter issues:
1. Check the Supabase dashboard logs
2. Verify your environment variables
3. Ensure all migrations have run successfully
4. Check that authentication is working properly

## Development Tips

1. **Use the Supabase dashboard** to monitor your database and test queries
2. **Enable database logs** in production to troubleshoot issues
3. **Regular backups**: Supabase provides automatic backups, but consider additional backup strategies for production
4. **Monitor usage**: Keep an eye on your database usage to avoid hitting limits

## Production Considerations

1. **Environment Variables**: Use secure environment variable management
2. **Database Backups**: Ensure regular backups are configured
3. **Performance Monitoring**: Monitor query performance and optimize as needed
4. **Security**: Regularly review and update RLS policies
5. **Scaling**: Monitor database performance and upgrade as needed