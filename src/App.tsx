import { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/sonner';
import { Layout } from '@/components/layout/Layout';
import { Landing } from '@/pages/Landing';
import { Dashboard } from '@/pages/Dashboard';
import { Collection } from '@/pages/Collection';
import { AddGame } from '@/pages/AddGame';
import { Settings } from '@/pages/Settings';
import { Wishlist } from '@/pages/Wishlist';
import { Recommendations } from '@/pages/Recommendations';
import { PriceTracker } from '@/pages/PriceTracker';
import { Login } from '@/pages/auth/Login';
import { Register } from '@/pages/auth/Register';
import { ForgotPassword } from '@/pages/auth/ForgotPassword';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Welcome } from '@/components/onboarding/Welcome';
import { useAuthStore } from '@/stores/useAuthStore';
import { useGameStore } from '@/stores/useGameStore';
import './App.css';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

export default function App() {
  const { initialize, user, showOnboarding, completeOnboarding } = useAuthStore();
  const { fetchUserGames } = useGameStore();

  useEffect(() => {
    initialize();
  }, [initialize]);

  useEffect(() => {
    if (user?.id) {
      fetchUserGames(user.id);
    }
  }, [user?.id, fetchUserGames]);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        <Router>
          <div className="min-h-screen bg-background text-foreground">
            {/* Show onboarding if user is authenticated and hasn't completed it */}
            {user && showOnboarding ? (
              <Welcome onComplete={completeOnboarding} />
            ) : (
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={user ? (
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                ) : <Landing />}>
                  {user && (
                    <>
                      <Route index element={<Dashboard />} />
                      <Route path="collection" element={<Collection />} />
                      <Route path="add-game" element={<AddGame />} />
                      <Route path="wishlist" element={<Wishlist />} />
                      <Route path="recommendations" element={<Recommendations />} />
                      <Route path="prices" element={<PriceTracker />} />
                      <Route path="settings" element={<Settings />} />
                    </>
                  )}
                </Route>
                
                {/* Auth Routes */}
                <Route path="/auth/login" element={<Login />} />
                <Route path="/auth/register" element={<Register />} />
                <Route path="/auth/forgot-password" element={<ForgotPassword />} />
                
                {/* Protected Dashboard Routes (alternative path) */}
                <Route path="/dashboard" element={
                  <ProtectedRoute>
                    <Layout />
                  </ProtectedRoute>
                }>
                  <Route index element={<Dashboard />} />
                  <Route path="collection" element={<Collection />} />
                  <Route path="add-game" element={<AddGame />} />
                  <Route path="wishlist" element={<Wishlist />} />
                  <Route path="recommendations" element={<Recommendations />} />
                  <Route path="prices" element={<PriceTracker />} />
                  <Route path="settings" element={<Settings />} />
                </Route>
              </Routes>
            )}
            <Toaster />
          </div>
        </Router>
      </ThemeProvider>
    </QueryClientProvider>
  );
}