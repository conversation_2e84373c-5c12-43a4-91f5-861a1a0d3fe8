import React from 'react';
import { motion } from 'framer-motion';
import { MoreHorizontal, Star, Clock, Check, Heart } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { UserGame } from '@/types';
import { cn } from '@/lib/utils';

interface GameCardProps {
  userGame: UserGame;
  onStatusChange: (id: string, status: string) => void;
  onToggleWishlist: (id: string) => void;
  onEdit: (userGame: UserGame) => void;
  onDelete: (id: string) => void;
}

const statusIcons = {
  playing: <Clock className="h-3 w-3" />,
  completed: <Check className="h-3 w-3" />,
  backlog: <Star className="h-3 w-3" />,
  wishlist: <Heart className="h-3 w-3" />
};

const statusColors = {
  playing: 'bg-blue-500',
  completed: 'bg-green-500',
  backlog: 'bg-yellow-500',
  wishlist: 'bg-red-500'
};

const platformColors = {
  'PC': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  'Xbox 360': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
};

export const GameCard: React.FC<GameCardProps> = ({
  userGame,
  onStatusChange,
  onToggleWishlist,
  onEdit,
  onDelete
}) => {
  const { game } = userGame;
  
  if (!game) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
    >
      <Card className="overflow-hidden group cursor-pointer hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-card/50 backdrop-blur-sm">
        <div className="relative">
          <div className="aspect-[3/4] bg-muted overflow-hidden">
            {game.cover_image ? (
              <img
                src={game.cover_image}
                alt={game.title}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Ik0xMiA5VjEzTTEyIDE3SDE2TTE2IDlIMTJNMTIgOUg4TTggOVY5TTggMTNIMTJNMTIgMTNWMTdNMTIgMTdIOCIgc3Ryb2tlPSIjOWNhM2FmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K';
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                No Cover
              </div>
            )}
          </div>
          
          {/* Status indicator */}
          <div className={cn(
            "absolute top-2 left-2 w-3 h-3 rounded-full",
            statusColors[userGame.status]
          )} />

          {/* Platform badge */}
          <Badge 
            variant="secondary" 
            className={cn(
              "absolute top-2 right-2 text-xs",
              platformColors[game.platform as keyof typeof platformColors]
            )}
          >
            {game.platform}
          </Badge>

          {/* Actions */}
          <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm border-0 shadow-sm"
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-[180px] bg-background/95 backdrop-blur-sm">
                <DropdownMenuItem
                  onClick={() => onStatusChange(userGame.id, 'playing')}
                  className="py-2 text-sm"
                >
                  Mark as Playing
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onStatusChange(userGame.id, 'completed')}
                  className="py-2 text-sm"
                >
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onStatusChange(userGame.id, 'backlog')}
                  className="py-2 text-sm"
                >
                  Add to Backlog
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onToggleWishlist(userGame.id)}
                  className="py-2 text-sm"
                >
                  {userGame.is_wishlist ? 'Remove from Wishlist' : 'Add to Wishlist'}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onEdit(userGame)}
                  className="py-2 text-sm"
                >
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(userGame.id)}
                  className="text-destructive py-2 text-sm"
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <CardContent className="p-4">
          <h3 className="font-semibold truncate mb-1 text-base">{game.title}</h3>
          
          <div className="flex items-center space-x-2 text-sm text-muted-foreground mb-2">
            {statusIcons[userGame.status]}
            <span className="capitalize">{userGame.status}</span>
          </div>

          {game.genres.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {game.genres.slice(0, 2).map((genre) => (
                <Badge key={genre} variant="outline" className="text-xs">
                  {genre}
                </Badge>
              ))}
              {game.genres.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{game.genres.length - 2}
                </Badge>
              )}
            </div>
          )}

          {userGame.personal_rating && (
            <div className="flex items-center space-x-1">
              <Star className="h-3 w-3 fill-current text-yellow-500" />
              <span className="text-sm">{userGame.personal_rating}/5</span>
            </div>
          )}

          {game.release_date && (
            <p className="text-xs text-muted-foreground mt-1">
              {new Date(game.release_date).getFullYear()}
            </p>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};