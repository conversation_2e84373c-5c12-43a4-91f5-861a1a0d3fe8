import React from 'react';
import { Filter, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Checkbox } from '@/components/ui/checkbox';
import { useGameStore } from '@/stores/useGameStore';
import { Platform, Genre } from '@/types';

const platforms: Platform[] = ['PC', 'Xbox 360'];
const genres: Genre[] = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Sports',
  'Racing', 'Simulation', 'Puzzle', 'Fighting', 'Shooter',
  'Horror', 'Platformer', 'Indie'
];

export const GameFilters: React.FC = () => {
  const { filters, updateFilters, sortBy, sortOrder, updateSort } = useGameStore();

  const clearAllFilters = () => {
    updateFilters({
      platform: undefined,
      genres: [],
      status: undefined,
      rating: undefined,
    });
  };

  const hasActiveFilters = filters.platform || filters.genres.length > 0 || filters.status || filters.rating;

  return (
    <div className="space-y-4 p-4 bg-card/50 backdrop-blur-sm rounded-lg border-0 shadow-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">Filters & Sort</span>
        </div>
        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button variant="ghost" size="sm" onClick={clearAllFilters}>
            <X className="h-4 w-4 mr-1" />
            Clear All
          </Button>
        )}
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:flex lg:flex-wrap items-center gap-3">
        {/* Platform Filter */}
        <Select
          value={filters.platform || ''}
          onValueChange={(value) => updateFilters({ platform: value as Platform || undefined })}
        >
          <SelectTrigger className="w-full lg:w-32 h-10 touch-manipulation">
            <SelectValue placeholder="Platform" />
          </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Platforms</SelectItem>
          {platforms.map((platform) => (
            <SelectItem key={platform} value={platform}>
              {platform}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

        {/* Genre Filter */}
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full lg:w-32 h-10 touch-manipulation justify-start">
              Genres
              {filters.genres.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {filters.genres.length}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
        <PopoverContent className="w-64">
          <div className="space-y-2">
            <h4 className="font-medium">Select Genres</h4>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {genres.map((genre) => (
                <div key={genre} className="flex items-center space-x-2">
                  <Checkbox
                    id={genre}
                    checked={filters.genres.includes(genre)}
                    onCheckedChange={(checked) => {
                      const newGenres = checked
                        ? [...filters.genres, genre]
                        : filters.genres.filter(g => g !== genre);
                      updateFilters({ genres: newGenres });
                    }}
                  />
                  <label htmlFor={genre} className="text-sm">
                    {genre}
                  </label>
                </div>
              ))}
            </div>
          </div>
        </PopoverContent>
      </Popover>

        {/* Status Filter */}
        <Select
          value={filters.status || 'all'}
          onValueChange={(value) => updateFilters({ status: value === 'all' ? undefined : value as any })}
        >
          <SelectTrigger className="w-full lg:w-32 h-10 touch-manipulation">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="playing">Playing</SelectItem>
          <SelectItem value="completed">Completed</SelectItem>
          <SelectItem value="backlog">Backlog</SelectItem>
        </SelectContent>
      </Select>

        {/* Sort */}
        <div className="col-span-1 sm:col-span-2 lg:col-span-1">
          <Select
            value={`${sortBy}-${sortOrder}`}
            onValueChange={(value) => {
              const [sortBy, sortOrder] = value.split('-');
              updateSort(sortBy, sortOrder as 'asc' | 'desc');
            }}
          >
            <SelectTrigger className="w-full lg:w-40 h-10 touch-manipulation">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
        <SelectContent>
          <SelectItem value="title-asc">Title A-Z</SelectItem>
          <SelectItem value="title-desc">Title Z-A</SelectItem>
          <SelectItem value="dateAdded-desc">Recently Added</SelectItem>
          <SelectItem value="dateAdded-asc">Oldest First</SelectItem>
          <SelectItem value="rating-desc">Highest Rated</SelectItem>
          <SelectItem value="rating-asc">Lowest Rated</SelectItem>
        </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};