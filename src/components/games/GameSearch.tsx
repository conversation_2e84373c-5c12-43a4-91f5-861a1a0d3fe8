import React, { useState, useEffect, useRef } from 'react';
import { Search, Loader2, ExternalLink } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { apiService } from '@/lib/api';
import { cn } from '@/lib/utils';

interface GameSearchResult {
  id: number;
  name: string;
  platforms?: Array<{ name: string }>;
  genres?: Array<{ name: string }>;
  cover?: { url: string };
  first_release_date?: number;
  involved_companies?: Array<{
    company: { name: string };
    developer: boolean;
  }>;
}

interface GameSearchProps {
  onGameSelect: (gameData: any) => void;
  className?: string;
}

export const GameSearch: React.FC<GameSearchProps> = ({ onGameSelect, className }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<GameSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const debounceRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const searchGames = async (term: string) => {
    if (term.length < 2) {
      setResults([]);
      setShowResults(false);
      setError(null);
      return;
    }

    setLoading(true);
    setError(null);
    try {
      const games = await apiService.searchGames(term, 8);
      console.log('=== GameSearch Component ===');
      console.log('Raw games received:', games);
      console.log('Games array length:', games?.length || 0);
      console.log('First game sample:', games?.[0]);
      setResults(games);
      setShowResults(true);
    } catch (error) {
      console.error('Search error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to search games';
      setError(errorMessage);
      setResults([]);
      setShowResults(true);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (value: string) => {
    setSearchTerm(value);
    
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    debounceRef.current = setTimeout(() => {
      searchGames(value);
    }, 300);
  };

  const handleGameSelect = async (game: GameSearchResult) => {
    setLoading(true);
    try {
      // Get detailed game data
      const detailedGame = await apiService.getGameDetails(game.id);
      if (detailedGame) {
        const gameData = apiService.convertIGDBToGame(detailedGame);
        
        // Fetch YouTube videos for this game
        const youtubeLinks = await apiService.searchYouTubeVideos(game.name);
        
        onGameSelect({
          ...gameData,
          youtube_links: [...(gameData.youtube_links || []), ...youtubeLinks].slice(0, 5)
        });
      }
      
      setShowResults(false);
      setSearchTerm('');
    } catch (error) {
      console.error('Error selecting game:', error);
    } finally {
      setLoading(false);
    }
  };

  const platformMap: Record<string, string> = {
    'PC (Microsoft Windows)': 'PC',
    'Mac': 'Mac',
    'PlayStation 5': 'PS5',
    'PlayStation 4': 'PS4',
    'PlayStation 3': 'PS3',
    'PlayStation 2': 'PS2',
    'PlayStation': 'PS1',
    'Xbox Series X/S': 'Xbox X/S',
    'Xbox One': 'Xbox One',
    'Xbox 360': 'Xbox 360',
    'Xbox': 'Xbox',
    'Nintendo Switch': 'Switch',
    'Nintendo 3DS': '3DS',
    'Nintendo DS': 'DS',
    'Wii U': 'Wii U',
    'Wii': 'Wii',
    'Nintendo GameCube': 'GameCube',
    'iOS': 'iOS',
    'Android': 'Android',
    'Steam Deck': 'Steam Deck'
  };

  const getSupportedPlatforms = (platforms?: Array<{ name: string }>) => {
    if (!platforms) return [];
    return platforms
      .map(p => platformMap[p.name])
      .filter(Boolean);
  };

  return (
    <div ref={searchRef} className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          placeholder="Search for games on IGDB..."
          value={searchTerm}
          onChange={(e) => handleInputChange(e.target.value)}
          className="pl-9 pr-10"
        />
        {loading && (
          <Loader2 className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 animate-spin text-muted-foreground" />
        )}
      </div>

      {showResults && results.length > 0 && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1 max-h-96 overflow-y-auto">
          <CardContent className="p-2">
            <div className="space-y-2">
              {results.map((game) => {
                const supportedPlatforms = getSupportedPlatforms(game.platforms);
                const developer = game.involved_companies?.find(ic => ic.developer)?.company.name;
                
                return (
                  <Button
                    key={game.id}
                    variant="ghost"
                    className="w-full h-auto p-3 justify-start"
                    onClick={() => handleGameSelect(game)}
                    disabled={false}
                  >
                    <div className="flex items-start space-x-3 w-full">
                      <div className="w-12 h-16 bg-muted rounded flex-shrink-0 overflow-hidden">
                        {game.cover?.url ? (
                          <img
                            src={`https:${game.cover.url.replace('t_thumb', 't_cover_small')}`}
                            alt={game.name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center text-xs text-muted-foreground">
                            No Cover
                          </div>
                        )}
                      </div>
                      
                      <div className="flex-1 text-left space-y-1">
                        <h4 className="font-medium text-sm line-clamp-1">{game.name}</h4>
                        
                        {developer && (
                          <p className="text-xs text-muted-foreground">{developer}</p>
                        )}
                        
                        {game.first_release_date && (
                          <p className="text-xs text-muted-foreground">
                            {new Date(game.first_release_date * 1000).getFullYear()}
                          </p>
                        )}
                        
                        <div className="flex flex-wrap gap-1">
                          {supportedPlatforms.slice(0, 4).map((platform) => (
                            <Badge key={platform} variant="secondary" className="text-xs">
                              {platform}
                            </Badge>
                          ))}
                          {supportedPlatforms.length > 4 && (
                            <Badge variant="secondary" className="text-xs">
                              +{supportedPlatforms.length - 4}
                            </Badge>
                          )}
                          {supportedPlatforms.length === 0 && (
                            <Badge variant="outline" className="text-xs">
                              Other Platforms
                            </Badge>
                          )}
                        </div>
                        
                        {game.genres && game.genres.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {game.genres.slice(0, 3).map((genre) => (
                              <Badge key={genre.name} variant="outline" className="text-xs">
                                {genre.name}
                              </Badge>
                            ))}
                            {game.genres.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{game.genres.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <ExternalLink className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    </div>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {showResults && results.length === 0 && !loading && searchTerm.length >= 2 && !error && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1">
          <CardContent className="p-4 text-center text-muted-foreground">
            No games found for "{searchTerm}"
          </CardContent>
        </Card>
      )}

      {showResults && error && (
        <Card className="absolute top-full left-0 right-0 z-50 mt-1">
          <CardContent className="p-4 text-center">
            <div className="text-destructive text-sm font-medium mb-2">
              Search Error
            </div>
            <div className="text-xs text-muted-foreground">
              {error}
            </div>
            {error.includes('credentials not configured') && (
              <div className="mt-2 text-xs text-muted-foreground">
                Please configure your IGDB API credentials in the .env file.
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};