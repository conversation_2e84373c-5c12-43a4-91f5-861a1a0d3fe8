import React from 'react';
import { <PERSON>, Github, Twitter, Mail, Gamepad2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

export const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-border/40 bg-gradient-to-r from-background via-background/98 to-background/95 backdrop-blur-xl">
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Gamepad2 className="h-8 w-8 text-primary drop-shadow-sm" />
                <div className="absolute -inset-1 bg-primary/20 rounded-lg blur-sm -z-10"></div>
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-transparent">
                GameVault
              </span>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Your personal gaming collection manager. Track, organize, and discover your next favorite game.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="/dashboard" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Dashboard
                </a>
              </li>
              <li>
                <a href="/collection" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  My Collection
                </a>
              </li>
              <li>
                <a href="/wishlist" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Wishlist
                </a>
              </li>
              <li>
                <a href="/recommendations" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Recommendations
                </a>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Support</h3>
            <ul className="space-y-2">
              <li>
                <a href="/help" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Help Center
                </a>
              </li>
              <li>
                <a href="/privacy" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Privacy Policy
                </a>
              </li>
              <li>
                <a href="/terms" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Terms of Service
                </a>
              </li>
              <li>
                <a href="/contact" className="text-sm text-muted-foreground hover:text-primary transition-colors duration-200">
                  Contact Us
                </a>
              </li>
            </ul>
          </div>

          {/* Social & Connect */}
          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Connect</h3>
            <div className="flex space-x-2">
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-9 w-9 p-0 rounded-xl hover:bg-accent/50 transition-all duration-300 group"
              >
                <Github className="h-4 w-4 transition-transform group-hover:scale-110" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-9 w-9 p-0 rounded-xl hover:bg-accent/50 transition-all duration-300 group"
              >
                <Twitter className="h-4 w-4 transition-transform group-hover:scale-110" />
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-9 w-9 p-0 rounded-xl hover:bg-accent/50 transition-all duration-300 group"
              >
                <Mail className="h-4 w-4 transition-transform group-hover:scale-110" />
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Follow us for updates and gaming news
            </p>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-6 border-t border-border/30 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <span>© {currentYear} GameVault. Made with</span>
            <Heart className="h-4 w-4 text-red-500 animate-pulse" />
            <span>for gamers</span>
          </div>
          <div className="text-xs text-muted-foreground">
            Version 1.0.0 • Built with React & TypeScript
          </div>
        </div>
      </div>
    </footer>
  );
};
