import React from 'react';
import { <PERSON>, <PERSON>, Moon, Sun } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useTheme } from 'next-themes';
import { useGameStore } from '@/stores/useGameStore';

interface HeaderProps {
  onSidebarToggle?: () => void;
}

export const Header: React.FC<HeaderProps> = () => {
  const { theme, setTheme } = useTheme();
  const { filters, updateFilters } = useGameStore();

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <header className="sticky top-0 z-40 flex h-16 items-center justify-between border-b border-border/40 bg-gradient-to-r from-background via-background/98 to-background/95 px-6 backdrop-blur-xl shadow-sm">
      <div className="flex items-center space-x-6">
        <div className="relative max-w-lg">
          <Search className="absolute left-4 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground transition-colors" />
          <Input
            type="search"
            placeholder="Search your games..."
            className="pl-12 pr-4 w-80 h-11 bg-background/50 border-border/50 rounded-xl focus:bg-background focus:border-primary/50 focus:ring-2 focus:ring-primary/20 transition-all duration-300 placeholder:text-muted-foreground/70"
            value={filters.searchQuery}
            onChange={(e) => updateFilters({ searchQuery: e.target.value })}
          />
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <Button
          variant="ghost"
          size="sm"
          className="h-10 w-10 p-0 rounded-xl hover:bg-accent/50 transition-all duration-300 relative group"
        >
          <Bell className="h-4 w-4 transition-transform group-hover:scale-110" />
          <div className="absolute -top-1 -right-1 h-2 w-2 bg-primary rounded-full animate-pulse"></div>
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleTheme}
          className="h-10 w-10 p-0 rounded-xl hover:bg-accent/50 transition-all duration-300 group"
        >
          {theme === 'dark' ? (
            <Sun className="h-4 w-4 transition-transform group-hover:scale-110 group-hover:rotate-12" />
          ) : (
            <Moon className="h-4 w-4 transition-transform group-hover:scale-110 group-hover:-rotate-12" />
          )}
        </Button>
      </div>
    </header>
  );
};