import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

export const Layout: React.FC = () => {
  const [isDesktop, setIsDesktop] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const desktop = window.innerWidth >= 1024;
      if (isDesktop !== desktop) {
        setIsDesktop(desktop);
        if (!desktop) {
          setSidebarOpen(false);
        }
      }
    };
    
    // Set initial state
    handleResize();
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isDesktop]);

  const toggleSidebar = () => {
    if (!isDesktop) {
      setSidebarOpen(!sidebarOpen);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Sidebar isOpen={isDesktop || sidebarOpen} onToggle={toggleSidebar} />
      <div className="lg:pl-80">
        <div className="flex flex-col min-h-screen">
          <Header onSidebarToggle={toggleSidebar} />
          
          <main className="flex-1 w-full bg-muted/30 min-h-[calc(100vh-4rem)]">
            <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 max-w-7xl">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};