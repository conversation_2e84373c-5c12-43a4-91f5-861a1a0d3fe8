import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

export const Layout: React.FC = () => {
  const toggleSidebar = () => {
    // No-op for desktop-only layout
  };

  return (
    <div className="min-h-screen bg-background">
      <Sidebar isOpen={true} onToggle={toggleSidebar} />
      <div className="pl-80">
        <div className="flex flex-col min-h-screen">
          <Header onSidebarToggle={toggleSidebar} />

          <main className="flex-1 w-full bg-muted/30 min-h-[calc(100vh-4rem)]">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
};