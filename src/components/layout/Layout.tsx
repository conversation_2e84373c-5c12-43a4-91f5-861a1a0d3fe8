import React from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { Footer } from './Footer';

export const Layout: React.FC = () => {
  const toggleSidebar = () => {
    // No-op for desktop-only layout
  };

  return (
    <div className="min-h-screen bg-background">
      <Sidebar />
      <div className="pl-72">
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-1 w-full bg-muted/30 min-h-[calc(100vh-4rem)]">
            <Outlet />
          </main>
          <Footer />
        </div>
      </div>
    </div>
  );
};