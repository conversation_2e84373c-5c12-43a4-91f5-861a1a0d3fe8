import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface PageContainerProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  description?: string;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '5xl' | '7xl' | 'full';
  spacing?: 'sm' | 'md' | 'lg';
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '5xl': 'max-w-5xl',
  '7xl': 'max-w-7xl',
  full: 'max-w-full'
};

const spacingClasses = {
  sm: 'space-y-4',
  md: 'space-y-6',
  lg: 'space-y-8'
};

export const PageContainer: React.FC<PageContainerProps> = ({
  children,
  className,
  title,
  description,
  maxWidth = '7xl',
  spacing = 'md'
}) => {
  return (
    <div className={cn(
      'w-full mx-auto px-8 py-8',
      maxWidthClasses[maxWidth],
      spacingClasses[spacing],
      className
    )}>
      {(title || description) && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="mb-8"
        >
          {title && (
            <h1 className="text-3xl font-bold tracking-tight text-foreground mb-2">
              {title}
            </h1>
          )}
          {description && (
            <p className="text-base text-muted-foreground max-w-2xl">
              {description}
            </p>
          )}
        </motion.div>
      )}
      {children}
    </div>
  );
};
