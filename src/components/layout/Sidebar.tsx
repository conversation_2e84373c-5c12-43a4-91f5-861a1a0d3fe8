import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  Home,
  Library,
  Plus,
  Heart,
  TrendingUp,
  DollarSign,
  Settings,
  Gamepad2,
  LogOut,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuthStore } from '@/stores/useAuthStore';

interface SidebarProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: Home },
  { name: 'My Collection', href: '/collection', icon: Library },
  { name: 'Add Game', href: '/add-game', icon: Plus },
  { name: 'Wishlist', href: '/wishlist', icon: Heart },
  { name: 'Recommendations', href: '/recommendations', icon: TrendingUp },
  { name: 'Price Tracker', href: '/prices', icon: DollarSign },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export const Sidebar: React.FC<SidebarProps> = () => {
  const location = useLocation();
  const { user, profile, signOut } = useAuthStore();

  const handleLogout = async () => {
    await signOut();
  };

  const getUserDisplayName = () => {
    if (profile?.display_name) return profile.display_name;
    if (profile?.username) return profile.username;
    if (user?.email) return user.email.split('@')[0];
    return 'User';
  };

  const getUserInitials = () => {
    const name = getUserDisplayName();
    return name.charAt(0).toUpperCase();
  };

  return (
    <aside className="fixed left-0 top-0 z-50 h-screen w-72 bg-gradient-to-b from-background via-background/98 to-background/95 backdrop-blur-xl border-r border-border/40 shadow-2xl shadow-black/5">

        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-border/30 bg-background/30 backdrop-blur-sm">
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.02 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <motion.div
                className="relative"
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Gamepad2 className="h-8 w-8 text-primary drop-shadow-sm" />
                <div className="absolute -inset-1 bg-primary/20 rounded-lg blur-sm -z-10"></div>
              </motion.div>
              <span className="text-xl font-bold bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-transparent">
                GameVault
              </span>
            </motion.div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-2 p-6 overflow-y-auto scrollbar-thin scrollbar-thumb-border scrollbar-track-transparent">
            {navigation.map((item, index) => {
              const isActive = location.pathname === item.href;
              return (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <NavLink
                    to={item.href}
                    className={cn(
                      "flex items-center space-x-3 rounded-xl px-4 py-3.5 text-sm font-medium transition-all duration-300 relative overflow-hidden group",
                      isActive
                        ? "bg-gradient-to-r from-primary to-primary/90 text-primary-foreground shadow-lg shadow-primary/25 border border-primary/20"
                        : "text-muted-foreground hover:bg-gradient-to-r hover:from-accent hover:to-accent/80 hover:text-accent-foreground hover:shadow-md hover:shadow-accent/10 hover:border hover:border-accent/20 active:scale-[0.98]"
                    )}
                  >
                    <motion.div
                      className="relative"
                      animate={isActive ? { rotate: [0, 360] } : {}}
                      transition={{ duration: 0.5, ease: "easeInOut" }}
                    >
                      <item.icon className={cn(
                        "h-5 w-5 transition-all duration-300",
                        isActive ? "drop-shadow-sm" : "group-hover:scale-110"
                      )} />
                      {isActive && (
                        <div className="absolute -inset-1 bg-primary-foreground/20 rounded-md blur-sm -z-10"></div>
                      )}
                    </motion.div>
                    <span className="font-medium tracking-wide">{item.name}</span>
                    {isActive && (
                      <motion.div
                        layoutId="sidebar-active-bg"
                        className="absolute inset-0 bg-primary/10 rounded-lg"
                        initial={false}
                        transition={{ type: "spring", stiffness: 500, damping: 30 }}
                      />
                    )}
                  </NavLink>
                </motion.div>
              );
            })}
          </nav>

          {/* User section */}
          <div className="p-6 border-t border-border/30 bg-background/30 backdrop-blur-sm">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full justify-start p-3 h-auto rounded-xl hover:bg-accent/50 transition-all duration-300 group">
                  <div className="flex items-center space-x-3 w-full">
                    <div className="relative">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-primary to-primary/80 flex items-center justify-center overflow-hidden shadow-lg shadow-primary/25 ring-2 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                        {profile?.avatar_url ? (
                          <img
                            src={profile.avatar_url}
                            alt={getUserDisplayName()}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <span className="text-sm font-bold text-primary-foreground">
                            {getUserInitials()}
                          </span>
                        )}
                      </div>
                      <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 bg-green-500 rounded-full border-2 border-background shadow-sm"></div>
                    </div>
                    <div className="flex-1 min-w-0 text-left">
                      <p className="text-sm font-semibold truncate group-hover:text-foreground transition-colors">{getUserDisplayName()}</p>
                      <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-64 p-2 bg-background/95 backdrop-blur-xl border border-border/50 shadow-xl">
                <DropdownMenuItem asChild>
                  <NavLink to="/settings" className="cursor-pointer rounded-lg p-3 hover:bg-accent/50 transition-all duration-200">
                    <User className="h-4 w-4 mr-3 text-primary" />
                    <span className="font-medium">Profile & Settings</span>
                  </NavLink>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="my-2 bg-border/50" />
                <DropdownMenuItem onClick={handleLogout} className="cursor-pointer rounded-lg p-3 hover:bg-destructive/10 hover:text-destructive transition-all duration-200">
                  <LogOut className="h-4 w-4 mr-3" />
                  <span className="font-medium">Sign out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </aside>
    );
  };