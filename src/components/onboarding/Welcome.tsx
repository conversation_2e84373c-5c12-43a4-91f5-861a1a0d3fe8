import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowRight, 
  ArrowLeft, 
  Library, 
  Plus, 
  Filter, 
  BarChart3,
  Check
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface WelcomeProps {
  onComplete: () => void;
}

const onboardingSteps = [
  {
    id: 1,
    title: 'Welcome to GameVault!',
    description: 'Your personal game collection manager. Let\'s get you started with a quick tour.',
    icon: Library,
    content: (
      <div className="text-center space-y-4">
        <div className="w-24 h-24 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
          <Library className="h-12 w-12 text-primary" />
        </div>
        <p className="text-muted-foreground">
          GameVault helps you organize, track, and discover games across all your platforms.
        </p>
      </div>
    )
  },
  {
    id: 2,
    title: 'Add Your Games',
    description: 'Start building your collection by adding games you own or want to play.',
    icon: Plus,
    content: (
      <div className="space-y-4">
        <div className="bg-muted rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-16 bg-primary/20 rounded flex items-center justify-center">
              <Plus className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h4 className="font-medium">Search & Add</h4>
              <p className="text-sm text-muted-foreground">
                Search our database of games or add manually
              </p>
            </div>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Use the "Add Game" page to search for games from IGDB or add custom entries.
        </p>
      </div>
    )
  },
  {
    id: 3,
    title: 'Organize & Filter',
    description: 'Keep track of your gaming progress and filter your collection.',
    icon: Filter,
    content: (
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-blue-50 dark:bg-blue-950/30 rounded-lg p-3 text-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mx-auto mb-2"></div>
            <span className="text-xs">Playing</span>
          </div>
          <div className="bg-green-50 dark:bg-green-950/30 rounded-lg p-3 text-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mx-auto mb-2"></div>
            <span className="text-xs">Completed</span>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Track your progress, filter by platform, genre, or status to find exactly what you're looking for.
        </p>
      </div>
    )
  },
  {
    id: 4,
    title: 'Track Your Progress',
    description: 'See your gaming stats and achievements on the dashboard.',
    icon: BarChart3,
    content: (
      <div className="space-y-4">
        <div className="bg-muted rounded-lg p-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">12</div>
              <div className="text-xs text-muted-foreground">Total Games</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">8</div>
              <div className="text-xs text-muted-foreground">Completed</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-orange-600">2</div>
              <div className="text-xs text-muted-foreground">Playing</div>
            </div>
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          Your dashboard shows collection stats, recently added games, and current progress.
        </p>
      </div>
    )
  }
];

export const Welcome: React.FC<WelcomeProps> = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const navigate = useNavigate();

  const handleNext = () => {
    if (currentStep < onboardingSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
      navigate('/add-game');
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSkip = () => {
    onComplete();
    navigate('/');
  };

  const step = onboardingSteps[currentStep];
  const isLastStep = currentStep === onboardingSteps.length - 1;

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <Card className="border-2">
            <CardContent className="p-8">
              {/* Progress indicators */}
              <div className="flex justify-center space-x-2 mb-8">
                {onboardingSteps.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index <= currentStep ? 'bg-primary' : 'bg-muted'
                    }`}
                  />
                ))}
              </div>

              {/* Step content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                  className="text-center space-y-6"
                >
                  <div>
                    <h2 className="text-2xl font-bold mb-2">{step.title}</h2>
                    <p className="text-muted-foreground">{step.description}</p>
                  </div>

                  <div className="min-h-[200px] flex items-center justify-center">
                    {step.content}
                  </div>
                </motion.div>
              </AnimatePresence>

              {/* Navigation */}
              <div className="flex justify-between items-center mt-8">
                <Button
                  variant="ghost"
                  onClick={currentStep === 0 ? handleSkip : handlePrevious}
                  className="flex items-center"
                >
                  {currentStep === 0 ? (
                    'Skip Tour'
                  ) : (
                    <>
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      Previous
                    </>
                  )}
                </Button>

                <Button onClick={handleNext} className="flex items-center">
                  {isLastStep ? (
                    <>
                      Get Started
                      <Check className="h-4 w-4 ml-1" />
                    </>
                  ) : (
                    <>
                      Next
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};