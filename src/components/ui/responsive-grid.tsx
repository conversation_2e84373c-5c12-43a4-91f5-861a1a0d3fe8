import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  animate?: boolean;
  staggerChildren?: boolean;
}

const gapClasses = {
  sm: 'gap-3',
  md: 'gap-4 lg:gap-6',
  lg: 'gap-6 lg:gap-8'
};

const getGridCols = (cols: ResponsiveGridProps['cols']) => {
  if (!cols) return 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6';
  
  const classes = [];
  
  if (cols.default) classes.push(`grid-cols-${cols.default}`);
  if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`);
  if (cols.md) classes.push(`md:grid-cols-${cols.md}`);
  if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`);
  if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`);
  if (cols['2xl']) classes.push(`2xl:grid-cols-${cols['2xl']}`);
  
  return classes.join(' ');
};

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols,
  gap = 'md',
  animate = true,
  staggerChildren = true
}) => {
  const gridContent = (
    <div className={cn(
      'grid',
      getGridCols(cols),
      gapClasses[gap],
      className
    )}>
      {React.Children.map(children, (child, index) => {
        if (animate && staggerChildren) {
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
            >
              {child}
            </motion.div>
          );
        }
        return child;
      })}
    </div>
  );

  if (animate && !staggerChildren) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.4 }}
      >
        {gridContent}
      </motion.div>
    );
  }

  return gridContent;
};

// Predefined grid configurations for common use cases
export const GameGrid: React.FC<Omit<ResponsiveGridProps, 'cols'>> = (props) => (
  <ResponsiveGrid
    cols={{
      default: 2,
      sm: 3,
      md: 4,
      lg: 5,
      xl: 6,
      '2xl': 7
    }}
    {...props}
  />
);

export const StatsGrid: React.FC<Omit<ResponsiveGridProps, 'cols'>> = (props) => (
  <ResponsiveGrid
    cols={{
      default: 1,
      sm: 2,
      lg: 4
    }}
    {...props}
  />
);

export const ContentGrid: React.FC<Omit<ResponsiveGridProps, 'cols'>> = (props) => (
  <ResponsiveGrid
    cols={{
      default: 1,
      md: 2,
      lg: 3
    }}
    {...props}
  />
);

export const TwoColumnGrid: React.FC<Omit<ResponsiveGridProps, 'cols'>> = (props) => (
  <ResponsiveGrid
    cols={{
      default: 1,
      lg: 2
    }}
    {...props}
  />
);
