import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface StandardCardProps {
  children?: React.ReactNode;
  title?: React.ReactNode;
  description?: string;
  footer?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  animate?: boolean;
  delay?: number;
}

const variantClasses = {
  default: 'border-0 shadow-sm bg-card/50 backdrop-blur-sm',
  elevated: 'border-0 shadow-lg bg-card/80 backdrop-blur-sm',
  outlined: 'border shadow-none bg-card',
  ghost: 'border-0 shadow-none bg-transparent'
};

const sizeClasses = {
  sm: {
    card: 'rounded-lg',
    header: 'p-4 pb-2',
    content: 'p-4 pt-0',
    footer: 'p-4 pt-0'
  },
  md: {
    card: 'rounded-xl',
    header: 'p-6 pb-4',
    content: 'p-6 pt-0',
    footer: 'p-6 pt-0'
  },
  lg: {
    card: 'rounded-xl',
    header: 'p-8 pb-6',
    content: 'p-8 pt-0',
    footer: 'p-8 pt-0'
  }
};

export const StandardCard: React.FC<StandardCardProps> = ({
  children,
  title,
  description,
  footer,
  className,
  contentClassName,
  variant = 'default',
  size = 'md',
  hover = true,
  animate = true,
  delay = 0
}) => {
  const cardContent = (
    <Card className={cn(
      variantClasses[variant],
      sizeClasses[size].card,
      hover && 'hover:shadow-lg transition-all duration-300',
      className
    )}>
      {(title || description) && (
        <CardHeader className={sizeClasses[size].header}>
          {title && (
            <CardTitle className="text-lg font-semibold leading-none tracking-tight">
              {title}
            </CardTitle>
          )}
          {description && (
            <CardDescription className="text-sm text-muted-foreground">
              {description}
            </CardDescription>
          )}
        </CardHeader>
      )}
      
      {children && (
        <CardContent className={cn(
          sizeClasses[size].content,
          !title && !description && sizeClasses[size].header, // Use header padding if no header
          contentClassName
        )}>
          {children}
        </CardContent>
      )}
      
      {footer && (
        <CardFooter className={sizeClasses[size].footer}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );

  if (animate) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, delay }}
        whileHover={hover ? { y: -2 } : undefined}
      >
        {cardContent}
      </motion.div>
    );
  }

  return cardContent;
};
