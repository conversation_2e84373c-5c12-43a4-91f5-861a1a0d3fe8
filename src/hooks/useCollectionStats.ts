import { useMemo } from 'react';
import { useGameStore } from '@/stores/useGameStore';
import { CollectionStats, Platform, Genre } from '@/types';

export const useCollectionStats = (): CollectionStats => {
  const { userGames } = useGameStore();

  return useMemo(() => {
    const totalGames = userGames.filter(ug => !ug.is_wishlist).length;
    const completedGames = userGames.filter(ug => ug.status === 'completed').length;
    const currentlyPlaying = userGames.filter(ug => ug.status === 'playing').length;
    const wishlistCount = userGames.filter(ug => ug.is_wishlist).length;

    const totalHours = userGames.reduce((sum, ug) => sum + (ug.hours_played || 0), 0);
    
    const ratingsSum = userGames
      .filter(ug => ug.personal_rating)
      .reduce((sum, ug) => sum + (ug.personal_rating || 0), 0);
    const ratingsCount = userGames.filter(ug => ug.personal_rating).length;
    const averageRating = ratingsCount > 0 ? ratingsSum / ratingsCount : 0;

    const platformBreakdown = userGames.reduce((acc, ug) => {
      const platform = ug.game?.platform as Platform;
      if (platform) {
        acc[platform] = (acc[platform] || 0) + 1;
      }
      return acc;
    }, {} as Record<Platform, number>);

    const genreBreakdown = userGames.reduce((acc, ug) => {
      ug.game?.genres.forEach(genre => {
        acc[genre] = (acc[genre] || 0) + 1;
      });
      return acc;
    }, {} as Record<Genre, number>);

    return {
      totalGames,
      completedGames,
      currentlyPlaying,
      wishlistCount,
      totalHours,
      averageRating,
      platformBreakdown,
      genreBreakdown
    };
  }, [userGames]);
};