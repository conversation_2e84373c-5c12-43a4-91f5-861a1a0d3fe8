import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Authentication helpers
export const auth = {
  signUp: async (email: string, password: string, username?: string) => {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: { username }
      }
    });
    return { data, error };
  },

  signIn: async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  },

  signOut: async () => {
    const { error } = await supabase.auth.signOut();
    return { error };
  },

  resetPassword: async (email: string) => {
    const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    });
    return { data, error };
  },

  getCurrentUser: () => supabase.auth.getUser(),
  
  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    return supabase.auth.onAuthStateChange(callback);
  }
};

// Database helpers
export const db = {
  games: {
    getAll: () => supabase.from('games').select('*'),
    getById: (id: string) => supabase.from('games').select('*').eq('id', id).single(),
    create: (game: any) => supabase.from('games').insert(game).select().single(),
    update: (id: string, updates: any) => supabase.from('games').update(updates).eq('id', id).select().single(),
    delete: (id: string) => supabase.from('games').delete().eq('id', id)
  },

  userGames: {
    getUserCollection: (userId: string) => 
      supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId),
    
    addToCollection: (userGame: any) => 
      supabase.from('user_games').insert(userGame).select().single(),
    
    updateStatus: (id: string, updates: any) => 
      supabase.from('user_games').update(updates).eq('id', id).select().single(),
    
    removeFromCollection: (id: string) => 
      supabase.from('user_games').delete().eq('id', id)
  },

  priceTracking: {
    getGamePrices: (gameId: string) => 
      supabase.from('price_tracking').select('*').eq('game_id', gameId),
    
    addPriceEntry: (priceData: any) => 
      supabase.from('price_tracking').insert(priceData).select().single()
  },

  userProfiles: {
    getProfile: (userId: string) => 
      supabase.from('user_profiles').select('*').eq('id', userId).single(),
    
    updateProfile: (userId: string, updates: any) => 
      supabase.from('user_profiles').update(updates).eq('id', userId).select().single(),
    
    createProfile: (profileData: any) => 
      supabase.from('user_profiles').insert(profileData).select().single()
  },

  userPreferences: {
    getPreferences: (userId: string) => 
      supabase.from('user_preferences').select('*').eq('user_id', userId).single(),
    
    updatePreferences: (userId: string, updates: any) => 
      supabase.from('user_preferences').update(updates).eq('user_id', userId).select().single(),
    
    createPreferences: (preferencesData: any) => 
      supabase.from('user_preferences').insert(preferencesData).select().single()
  },

  stats: {
    getUserCollectionStats: (userId: string) => 
      supabase.from('user_collection_stats').select('*').eq('user_id', userId).single()
  }
};

// Storage helpers
export const storage = {
  uploadGameCover: async (file: File, gameId: string) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${gameId}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('game-covers')
      .upload(fileName, file, { upsert: true });
    
    if (error) return { data: null, error };
    
    const { data: publicUrl } = supabase.storage
      .from('game-covers')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  uploadScreenshot: async (file: File, gameId: string, index: number) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${gameId}_screenshot_${index}.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('game-screenshots')
      .upload(fileName, file, { upsert: true });
    
    if (error) return { data: null, error };
    
    const { data: publicUrl } = supabase.storage
      .from('game-screenshots')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  uploadUserAvatar: async (file: File, userId: string) => {
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}/avatar.${fileExt}`;
    
    const { data, error } = await supabase.storage
      .from('user-avatars')
      .upload(fileName, file, { upsert: true });
    
    if (error) return { data: null, error };
    
    const { data: publicUrl } = supabase.storage
      .from('user-avatars')
      .getPublicUrl(fileName);
    
    return { data: publicUrl.publicUrl, error: null };
  },

  deleteUserAvatar: async (userId: string) => {
    const { data, error } = await supabase.storage
      .from('user-avatars')
      .remove([`${userId}/avatar.jpg`, `${userId}/avatar.png`, `${userId}/avatar.webp`]);
    
    return { data, error };
  }
};