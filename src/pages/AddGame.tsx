import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload, Plus, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { PageContainer } from '@/components/layout/PageContainer';
import { StandardCard } from '@/components/ui/standard-card';
import { TwoColumnGrid } from '@/components/ui/responsive-grid';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Platform, Genre } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useAuthStore } from '@/stores/useAuthStore';
import { useGameStore } from '@/stores/useGameStore';
import { db, storage } from '@/lib/supabase';
import { GameSearch } from '@/components/games/GameSearch';
import { cn } from '@/lib/utils';

const platforms: Platform[] = ['PC', 'Xbox 360'];
const genres: Genre[] = [
  'Action', 'Adventure', 'RPG', 'Strategy', 'Sports',
  'Racing', 'Simulation', 'Puzzle', 'Fighting', 'Shooter',
  'Horror', 'Platformer', 'Indie'
];

const gameSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  platform: z.enum(['PC', 'Xbox 360']),
  genres: z.array(z.string()).min(1, 'At least one genre is required'),
  developer: z.string().optional(),
  publisher: z.string().optional(),
  releaseDate: z.string().optional(),
  description: z.string().optional(),
  metacriticScore: z.number().min(0).max(100).optional(),
  youtubeLinks: z.array(z.string().url()).optional(),
});

type GameFormData = z.infer<typeof gameSchema>;

export const AddGame: React.FC = () => {
  const [selectedGenres, setSelectedGenres] = useState<Genre[]>([]);
  const [youtubeLinks, setYoutubeLinks] = useState<string[]>(['']);
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [screenshots, setScreenshots] = useState<File[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [useIGDBData, setUseIGDBData] = useState(false);
  const [igdbData, setIgdbData] = useState<any>(null);
  const { toast } = useToast();
  const { user } = useAuthStore();
  const { addToUserCollection } = useGameStore();

  const form = useForm<GameFormData>({
    resolver: zodResolver(gameSchema),
    defaultValues: {
      title: '',
      genres: [],
      youtubeLinks: [],
    },
  });

  const handleGenreToggle = (genre: Genre) => {
    setSelectedGenres(prev => {
      const newGenres = prev.includes(genre)
        ? prev.filter(g => g !== genre)
        : [...prev, genre];
      form.setValue('genres', newGenres);
      return newGenres;
    });
  };

  const handleYoutubeLinkChange = (index: number, value: string) => {
    const newLinks = [...youtubeLinks];
    newLinks[index] = value;
    setYoutubeLinks(newLinks);
    form.setValue('youtubeLinks', newLinks.filter(link => link.trim() !== ''));
  };

  const addYoutubeLink = () => {
    setYoutubeLinks(prev => [...prev, '']);
  };

  const removeYoutubeLink = (index: number) => {
    setYoutubeLinks(prev => prev.filter((_, i) => i !== index));
  };

  const handleCoverImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setCoverImage(file);
    }
  };

  const handleScreenshotChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setScreenshots(prev => [...prev, ...files].slice(0, 5)); // Limit to 5 screenshots
  };

  const removeScreenshot = (index: number) => {
    setScreenshots(prev => prev.filter((_, i) => i !== index));
  };

  const handleGameSelect = (gameData: any) => {
    setIgdbData(gameData);
    setUseIGDBData(true);
    
    // Populate form with IGDB data
    if (gameData.title) form.setValue('title', gameData.title);
    if (gameData.platform) form.setValue('platform', gameData.platform);
    if (gameData.genres) {
      setSelectedGenres(gameData.genres);
      form.setValue('genres', gameData.genres);
    }
    if (gameData.developer) form.setValue('developer', gameData.developer);
    if (gameData.publisher) form.setValue('publisher', gameData.publisher);
    if (gameData.release_date) form.setValue('releaseDate', gameData.release_date);
    if (gameData.description) form.setValue('description', gameData.description);
    if (gameData.metacritic_score) form.setValue('metacriticScore', gameData.metacritic_score);
    if (gameData.youtube_links) {
      setYoutubeLinks([...gameData.youtube_links, '']);
      form.setValue('youtubeLinks', gameData.youtube_links);
    }

    toast({
      title: 'Game Data Loaded',
      description: `Loaded data for "${gameData.title}" from IGDB.`,
    });
  };

  const clearIGDBData = () => {
    setIgdbData(null);
    setUseIGDBData(false);
    form.reset();
    setSelectedGenres([]);
    setYoutubeLinks(['']);
    setCoverImage(null);
    setScreenshots([]);
  };

  const onSubmit = async (data: GameFormData) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to add games.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Use IGDB data if available, otherwise use form data
      const finalGameData = useIGDBData && igdbData ? {
        ...igdbData,
        // Override with any manual changes from the form
        title: data.title,
        platform: data.platform,
        genres: data.genres,
        developer: data.developer || igdbData.developer,
        publisher: data.publisher || igdbData.publisher,
        release_date: data.releaseDate || igdbData.release_date,
        description: data.description || igdbData.description,
        metacritic_score: data.metacriticScore || igdbData.metacritic_score,
        youtube_links: data.youtubeLinks?.filter(link => link.trim() !== '') || igdbData.youtube_links || [],
      } : {
        title: data.title,
        platform: data.platform,
        genres: data.genres,
        developer: data.developer || null,
        publisher: data.publisher || null,
        release_date: data.releaseDate || null,
        description: data.description || null,
        metacritic_score: data.metacriticScore || null,
        youtube_links: data.youtubeLinks?.filter(link => link.trim() !== '') || [],
      };

      // Create the game in the database
      const gameDataForDB = {
        ...finalGameData,
        cover_image: igdbData?.cover_image || null,
        screenshots: igdbData?.screenshots || [],
        igdb_id: igdbData?.igdb_id || null,
      };

      const { data: createdGame, error: gameError } = await db.games.create(gameDataForDB);
      if (gameError) throw gameError;

      let coverImageUrl = null;
      let screenshotUrls: string[] = [];

      // Upload cover image if provided
      if (coverImage && createdGame) {
        const { data: uploadData, error: uploadError } = await storage.uploadGameCover(
          coverImage,
          createdGame.id
        );
        if (uploadError) {
          console.error('Cover image upload error:', uploadError);
        } else {
          coverImageUrl = uploadData;
        }
      }

      // Upload screenshots if provided
      if (screenshots.length > 0 && createdGame) {
        const uploadPromises = screenshots.map((screenshot, index) =>
          storage.uploadScreenshot(screenshot, createdGame.id, index)
        );
        
        const uploadResults = await Promise.allSettled(uploadPromises);
        screenshotUrls = uploadResults
          .filter((result): result is PromiseFulfilledResult<{ data: string | null; error: any }> => 
            result.status === 'fulfilled' && result.value.data !== null
          )
          .map(result => result.value.data!);
      }

      // Update game with uploaded media URLs
      if (coverImageUrl || screenshotUrls.length > 0) {
        const updates: any = {};
        if (coverImageUrl) updates.cover_image = coverImageUrl;
        if (screenshotUrls.length > 0) updates.screenshots = screenshotUrls;

        const { error: updateError } = await db.games.update(createdGame.id, updates);
        if (updateError) {
          console.error('Error updating game with media:', updateError);
        }
      }

      // Add game to user's collection
      const userGameData = {
        user_id: user.id,
        game_id: createdGame.id,
        status: 'backlog' as const,
        is_wishlist: false,
      };

      const { data: userGame, error: userGameError } = await db.userGames.addToCollection(userGameData);
      if (userGameError) throw userGameError;

      // Update local state
      if (userGame) {
        const userGameWithGame = {
          ...userGame,
          game: {
            ...createdGame,
            cover_image: coverImageUrl || createdGame.cover_image,
            screenshots: screenshotUrls.length > 0 ? screenshotUrls : createdGame.screenshots,
          }
        };
        addToUserCollection(userGameWithGame);
      }

      toast({
        title: 'Game Added',
        description: `${finalGameData.title} has been added to your collection.`,
      });

      // Reset form
      form.reset();
      setSelectedGenres([]);
      setYoutubeLinks(['']);
      setCoverImage(null);
      setScreenshots([]);
      clearIGDBData();
    } catch (error) {
      console.error('Error adding game:', error);
      toast({
        title: 'Error',
        description: 'Failed to add game. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <PageContainer
      title="Add Game"
      description="Add a new game to your collection"
      maxWidth="5xl"
      spacing="md"
    >
      {/* Game Search */}
      <StandardCard
        title="Search Game Database"
        description="Search IGDB for game information to auto-populate the form"
        size="md"
        hover={true}
        animate={true}
        delay={0}
      >
        <GameSearch onGameSelect={handleGameSelect} />
        {useIGDBData && igdbData && (
          <div className="mt-4 p-3 bg-green-50 dark:bg-green-950 rounded-lg border border-green-200 dark:border-green-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Using data from IGDB for "{igdbData.title}"
                </span>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={clearIGDBData}
                className="text-green-700 dark:text-green-300 hover:text-green-900 dark:hover:text-green-100"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            </div>
          </div>
        )}
      </StandardCard>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <TwoColumnGrid gap="md" animate={false}>
            {/* Basic Information */}
            <StandardCard
              title="Basic Information"
              size="md"
              hover={true}
              animate={true}
              delay={0.1}
              className="h-fit"
            >
              <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title *</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter game title" 
                            {...field}
                            className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="platform"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Platform *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}>
                              <SelectValue placeholder="Select platform" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {platforms.map((platform) => (
                              <SelectItem key={platform} value={platform}>
                                {platform}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="developer"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Developer</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter developer name" 
                            {...field}
                            className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="publisher"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Publisher</FormLabel>
                        <FormControl>
                          <Input 
                            placeholder="Enter publisher name" 
                            {...field}
                            className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="releaseDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Release Date</FormLabel>
                        <FormControl>
                          <Input 
                            type="date" 
                            {...field}
                            className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="metacriticScore"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Metacritic Score</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            className={useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""}
                            min="0"
                            max="100"
                            placeholder="0-100"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
              </div>
            </StandardCard>

            {/* Additional Details */}
            <div className="space-y-4 lg:space-y-6">
              {/* Genres */}
              <StandardCard
                title={
                  <div className="flex items-center">
                    <span>Genres *</span>
                    {useIGDBData && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        From IGDB
                      </Badge>
                    )}
                  </div>
                }
                size="md"
                hover={true}
                animate={true}
                delay={0.2}
              >
                  <div className="flex flex-wrap gap-2">
                    {genres.map((genre) => (
                      <Badge
                        key={genre}
                        variant={selectedGenres.includes(genre) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => handleGenreToggle(genre)}
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                {form.formState.errors.genres && (
                  <p className="text-sm text-destructive mt-2">
                    {form.formState.errors.genres.message}
                  </p>
                )}
              </StandardCard>

              {/* Cover Image */}
              <StandardCard
                title={
                  <div className="flex items-center">
                    <span>Cover Image</span>
                    {useIGDBData && igdbData?.cover_image && (
                      <Badge variant="secondary" className="ml-2 text-xs">
                        From IGDB
                      </Badge>
                    )}
                  </div>
                }
                size="md"
                hover={true}
                animate={true}
                delay={0.3}
              >
                {useIGDBData && igdbData?.cover_image && (
                  <div className="mb-4">
                    <img
                      src={igdbData.cover_image}
                      alt="Cover from IGDB"
                      className="w-32 h-44 object-cover rounded-lg border"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Cover image from IGDB (you can still upload a custom one below)
                    </p>
                  </div>
                )}
                <div className="space-y-4">
                  <div className="flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                    <div className="text-center">
                      <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                      <div className="text-sm text-muted-foreground">
                        <label htmlFor="cover-upload" className="cursor-pointer hover:text-primary">
                          {useIGDBData && igdbData?.cover_image
                            ? 'Click to upload custom cover image'
                            : 'Click to upload cover image'
                          }
                        </label>
                        <input
                          id="cover-upload"
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={handleCoverImageChange}
                        />
                      </div>
                    </div>
                  </div>
                  {coverImage && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">
                        {coverImage.name}
                      </span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => setCoverImage(null)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </StandardCard>
            </div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.3 }}
          >
            <Card className="border-0 shadow-sm bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>
                  Description
                  {useIGDBData && igdbData?.description && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      From IGDB
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Textarea
                          placeholder="Enter game description..."
                          className={cn(
                            "min-h-[100px]",
                            useIGDBData ? "bg-blue-50 dark:bg-blue-950" : ""
                          )}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </motion.div>

          {/* YouTube Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            <Card className="border-0 shadow-sm bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>
                  YouTube Links
                  {useIGDBData && igdbData?.youtube_links?.length > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {igdbData.youtube_links.length} from IGDB
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {youtubeLinks.map((link, index) => (
                  <div key={index} className="flex space-x-2">
                    <Input
                      placeholder="YouTube video URL"
                      value={link}
                      className={useIGDBData && index < (igdbData?.youtube_links?.length || 0) ? "bg-blue-50 dark:bg-blue-950" : ""}
                      onChange={(e) => handleYoutubeLinkChange(index, e.target.value)}
                    />
                    {youtubeLinks.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeYoutubeLink(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addYoutubeLink}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add YouTube Link
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Screenshots */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.5 }}
          >
            <Card className="border-0 shadow-sm bg-card/50 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>
                  Screenshots (Max 5)
                  {useIGDBData && igdbData?.screenshots?.length > 0 && (
                    <Badge variant="secondary" className="ml-2 text-xs">
                      {igdbData.screenshots.length} from IGDB
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {useIGDBData && igdbData?.screenshots?.length > 0 && (
                  <div className="mb-4">
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {igdbData.screenshots.slice(0, 6).map((screenshot: string, index: number) => (
                        <img
                          key={index}
                          src={screenshot}
                          alt={`Screenshot ${index + 1}`}
                          className="w-full h-20 object-cover rounded border"
                        />
                      ))}
                    </div>
                    <p className="text-xs text-muted-foreground mt-2">
                      Screenshots from IGDB (you can still upload additional ones below)
                    </p>
                  </div>
                )}
                <div className="space-y-4">
                  {screenshots.length < 5 && (
                    <div className="flex items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                      <div className="text-center">
                        <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                        <div className="text-sm text-muted-foreground">
                          <label htmlFor="screenshot-upload" className="cursor-pointer hover:text-primary">
                            {useIGDBData && igdbData?.screenshots?.length > 0
                              ? 'Click to upload additional screenshots'
                              : 'Click to upload screenshots'
                            }
                          </label>
                          <input
                            id="screenshot-upload"
                            type="file"
                            accept="image/*"
                            multiple
                            className="hidden"
                            onChange={handleScreenshotChange}
                          />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {screenshots.length > 0 && (
                    <div className="space-y-2">
                      {screenshots.map((file, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground flex-1">
                            {file.name}
                          </span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeScreenshot(index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
            </div>
          </TwoColumnGrid>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: 0.6 }}
            className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-4"
          >
            <Button type="button" variant="outline" onClick={() => form.reset()} className="w-full sm:w-auto">
              Cancel
            </Button>
            {useIGDBData && (
              <Button type="button" variant="outline" onClick={clearIGDBData} className="w-full sm:w-auto">
                Clear IGDB Data
              </Button>
            )}
            <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? 'Adding Game...' : 'Add Game'}
            </Button>
          </motion.div>
        </form>
      </Form>
    </PageContainer>
  );
};