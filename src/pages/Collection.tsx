import React, { useMemo } from 'react';
import { GameCard } from '@/components/games/GameCard';
import { GameFilters } from '@/components/games/GameFilters';
import { PageContainer } from '@/components/layout/PageContainer';
import { GameGrid } from '@/components/ui/responsive-grid';
import { useGameStore } from '@/stores/useGameStore';


export const Collection: React.FC = () => {
  const { userGames, filters, sortBy, sortOrder, updateUserGame, removeFromUserCollection, loading } = useGameStore();
  // const [selectedGame, setSelectedGame] = useState<UserGame | null>(null);

  const filteredAndSortedGames = useMemo(() => {
    const filtered = userGames.filter(userGame => {
      if (!userGame.game) return false;
      
      // Filter out wishlist items from main collection view
      if (userGame.is_wishlist) return false;

      // Search filter
      if (filters.searchQuery) {
        const query = filters.searchQuery.toLowerCase();
        if (!userGame.game.title.toLowerCase().includes(query)) {
          return false;
        }
      }

      // Platform filter
      if (filters.platform && userGame.game.platform !== filters.platform) {
        return false;
      }

      // Genre filter
      if (filters.genres.length > 0) {
        const hasMatchingGenre = filters.genres.some(genre =>
          userGame.game!.genres.includes(genre)
        );
        if (!hasMatchingGenre) return false;
      }

      // Status filter
      if (filters.status && userGame.status !== filters.status) {
        return false;
      }

      // Rating filter
      if (filters.rating && (!userGame.personal_rating || userGame.personal_rating < filters.rating)) {
        return false;
      }

      return true;
    });

    // Sort
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'title':
          comparison = (a.game?.title || '').localeCompare(b.game?.title || '');
          break;
        case 'dateAdded':
          comparison = new Date(a.date_added).getTime() - new Date(b.date_added).getTime();
          break;
        case 'rating':
          comparison = (a.personal_rating || 0) - (b.personal_rating || 0);
          break;
        case 'releaseDate':
          const dateA = a.game?.release_date ? new Date(a.game.release_date).getTime() : 0;
          const dateB = b.game?.release_date ? new Date(b.game.release_date).getTime() : 0;
          comparison = dateA - dateB;
          break;
        default:
          comparison = 0;
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [userGames, filters, sortBy, sortOrder]);

  const handleStatusChange = (id: string, status: string) => {
    const updateData = { status: status as any };
    updateUserGame(id, updateData);
    
    // Also update in database
    import('@/lib/supabase').then(({ db }) => {
      db.userGames.updateStatus(id, updateData);
    });
  };

  const handleToggleWishlist = (id: string) => {
    const userGame = userGames.find(ug => ug.id === id);
    if (userGame) {
      const updateData = { is_wishlist: !userGame.is_wishlist };
      updateUserGame(id, updateData);
      
      // Also update in database
      import('@/lib/supabase').then(({ db }) => {
        db.userGames.updateStatus(id, updateData);
      });
    }
  };

  const handleEdit = (_userGame: UserGame) => {
    // setSelectedGame(userGame);
    // TODO: Open edit modal
  };

  const handleDelete = (id: string) => {
    removeFromUserCollection(id);
  };

  return (
    <PageContainer
      title="My Collection"
      description={`Manage your game library (${filteredAndSortedGames.length} games)`}
      spacing="md"
    >
      <GameFilters />

      {loading && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">Loading your collection...</p>
        </div>
      )}

      {filteredAndSortedGames.length > 0 ? (
        <GameGrid gap="md" animate={true} staggerChildren={true}>
          {filteredAndSortedGames.map((userGame) => (
            <GameCard
              key={userGame.id}
              userGame={userGame}
              onStatusChange={handleStatusChange}
              onToggleWishlist={handleToggleWishlist}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </GameGrid>
      ) : !loading && (
        <div className="text-center py-16 lg:py-20">
          <p className="text-muted-foreground text-lg mb-4">
            {userGames.length === 0 
              ? "Your game collection is empty"
              : "No games match your current filters"
            }
          </p>
          <p className="text-muted-foreground">
            {userGames.length === 0 
              ? "Start building your collection by adding some games!"
              : "Try adjusting your filters to see more games"
            }
          </p>
        </div>
      )}
    </PageContainer>
  );
};