import React from 'react';
import { motion } from 'framer-motion';
import { Library, Trophy, Clock, Heart, TrendingUp, Star } from 'lucide-react';

import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { PageContainer } from '@/components/layout/PageContainer';
import { StandardCard } from '@/components/ui/standard-card';
import { StatsGrid, TwoColumnGrid } from '@/components/ui/responsive-grid';
import { useCollectionStats } from '@/hooks/useCollectionStats';
import { useGameStore } from '@/stores/useGameStore';

export const Dashboard: React.FC = () => {
  const stats = useCollectionStats();
  const { userGames } = useGameStore();

  const recentGames = userGames
    .filter(ug => !ug.is_wishlist)
    .sort((a, b) => new Date(b.date_added).getTime() - new Date(a.date_added).getTime())
    .slice(0, 5);

  const currentlyPlaying = userGames.filter(ug => ug.status === 'playing').slice(0, 3);

  const completionRate = stats.totalGames > 0 ? (stats.completedGames / stats.totalGames) * 100 : 0;

  const statCards = [
    {
      title: 'Total Games',
      value: stats.totalGames,
      icon: Library,
      color: 'text-blue-600'
    },
    {
      title: 'Completed',
      value: stats.completedGames,
      icon: Trophy,
      color: 'text-green-600'
    },
    {
      title: 'Currently Playing',
      value: stats.currentlyPlaying,
      icon: Clock,
      color: 'text-orange-600'
    },
    {
      title: 'Wishlist',
      value: stats.wishlistCount,
      icon: Heart,
      color: 'text-red-600'
    }
  ];

  return (
    <PageContainer
      title="Dashboard"
      description="Welcome back! Here's your gaming overview."
      spacing="lg"
    >

      {/* Stats Grid */}
      <StatsGrid gap="md">
        {statCards.map((stat, index) => (
          <StandardCard
            key={stat.title}
            size="md"
            hover={true}
            animate={true}
            delay={index * 0.1}
            className="h-full"
          >
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </p>
                <p className="text-3xl font-bold">{stat.value}</p>
              </div>
              <div className={`p-3 rounded-xl bg-gradient-to-br from-background to-muted`}>
                <stat.icon className={`h-6 w-6 ${stat.color}`} />
              </div>
            </div>
          </StandardCard>
        ))}
      </StatsGrid>

      <TwoColumnGrid gap="lg" animate={false}>
        {/* Completion Progress */}
        <StandardCard
          title={
            <div className="flex items-center space-x-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-green-500/10 to-emerald-500/10">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <span>Progress Overview</span>
            </div>
          }
          size="md"
          hover={true}
          animate={true}
          delay={0.2}
          className="h-full"
        >
          <div className="space-y-6">
            <div>
              <div className="flex justify-between text-sm mb-3">
                <span className="font-medium">Completion Rate</span>
                <span className="font-bold text-green-600">{completionRate.toFixed(1)}%</span>
              </div>
              <Progress value={completionRate} className="h-3" />
            </div>

            <div className="grid grid-cols-2 gap-3 lg:gap-4">
              <div className="text-center p-4 rounded-lg bg-muted/50">
                <div className="text-xl lg:text-2xl font-bold">{stats.totalHours}h</div>
                <div className="text-sm text-muted-foreground">Total Hours</div>
              </div>
              <div className="text-center p-4 rounded-lg bg-muted/50">
                <div className="flex items-center justify-center space-x-1">
                  <Star className="h-4 w-4 fill-current text-yellow-500" />
                  <span className="text-xl lg:text-2xl font-bold">{stats.averageRating.toFixed(1)}</span>
                </div>
                <div className="text-sm text-muted-foreground">Avg Rating</div>
              </div>
            </div>
          </div>
        </StandardCard>

        {/* Currently Playing */}
        <StandardCard
          title={
            <div className="flex items-center space-x-2">
              <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500/10 to-red-500/10">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <span>Currently Playing</span>
            </div>
          }
          size="md"
          hover={true}
          animate={true}
          delay={0.3}
          className="h-full"
        >
          {currentlyPlaying.length > 0 ? (
            <div className="space-y-3 lg:space-y-4">
              {currentlyPlaying.map((userGame, index) => (
                <motion.div
                  key={userGame.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                  className="flex items-center space-x-4 p-3 rounded-lg hover:bg-accent transition-colors cursor-pointer"
                >
                  <div className="w-12 h-16 bg-muted rounded-lg flex-shrink-0 overflow-hidden shadow-sm">
                    {userGame.game?.cover_image ? (
                      <img
                        src={userGame.game.cover_image}
                        alt={userGame.game.title}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-xs text-muted-foreground">
                        No Cover
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-semibold truncate text-base">{userGame.game?.title}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {userGame.game?.platform}
                      </Badge>
                      {userGame.hours_played && (
                        <span className="text-xs text-muted-foreground">
                          {userGame.hours_played}h played
                        </span>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
              <p className="text-muted-foreground">
                No games currently being played
              </p>
              <p className="text-sm text-muted-foreground mt-1">
                Start playing a game to see it here
              </p>
            </div>
          )}
        </StandardCard>
      </TwoColumnGrid>

      {/* Recent Games */}
      <StandardCard
        title={
          <div className="flex items-center space-x-2">
            <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500/10 to-purple-500/10">
              <Library className="h-5 w-5 text-blue-600" />
            </div>
            <span>Recently Added</span>
          </div>
        }
        size="md"
        hover={true}
        animate={true}
        delay={0.4}
      >
        {recentGames.length > 0 ? (
          <div className="grid grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
            {recentGames.map((userGame, index) => (
              <motion.div
                key={userGame.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                whileHover={{ scale: 1.05 }}
                className="group cursor-pointer"
              >
                <div className="space-y-3">
                  <div className="aspect-[3/4] bg-muted rounded-xl overflow-hidden shadow-sm group-hover:shadow-lg transition-all duration-300">
                    {userGame.game?.cover_image ? (
                      <img
                        src={userGame.game.cover_image}
                        alt={userGame.game.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                        <Library className="h-8 w-8" />
                      </div>
                    )}
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold text-sm truncate group-hover:text-primary transition-colors">
                      {userGame.game?.title}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(userGame.date_added).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Library className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <p className="text-lg font-medium text-muted-foreground mb-2">
              No games in your collection yet
            </p>
            <p className="text-sm text-muted-foreground">
              Start by adding some games to track your progress!
            </p>
          </div>
        )}
      </StandardCard>
    </PageContainer>
  );
};