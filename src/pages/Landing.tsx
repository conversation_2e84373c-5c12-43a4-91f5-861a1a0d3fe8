import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Gamepad2, 
  Library, 
  TrendingUp, 
  Star, 
  Heart, 
  DollarSign,
  Users,
  Shield,
  Zap,
  ArrowRight,
  Play,
  BarChart3
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const features = [
  {
    icon: Library,
    title: 'Game Collection',
    description: 'Organize your entire game library across all platforms in one place.',
    color: 'text-blue-500'
  },
  {
    icon: TrendingUp,
    title: 'Progress Tracking',
    description: 'Track your gaming progress, completion rates, and hours played.',
    color: 'text-green-500'
  },
  {
    icon: Star,
    title: 'Ratings & Reviews',
    description: 'Rate your games and keep personal notes about your experiences.',
    color: 'text-yellow-500'
  },
  {
    icon: Heart,
    title: 'Wishlist',
    description: 'Save games you want to play and get notified about price drops.',
    color: 'text-red-500'
  },
  {
    icon: DollarSign,
    title: 'Price Tracking',
    description: 'Monitor game prices across multiple stores and find the best deals.',
    color: 'text-emerald-500'
  },
  {
    icon: BarChart3,
    title: 'Analytics',
    description: 'Get insights into your gaming habits with detailed statistics.',
    color: 'text-purple-500'
  }
];

const stats = [
  { label: 'Games Tracked', value: '50K+' },
  { label: 'Active Users', value: '10K+' },
  { label: 'Platforms Supported', value: '15+' },
  { label: 'Hours Logged', value: '1M+' }
];

export const Landing: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
      {/* Navigation */}
      <nav className="border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Gamepad2 className="h-8 w-8 text-primary" />
              <span className="text-xl font-bold">GameVault</span>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/auth/login">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link to="/auth/register">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="py-16 md:py-24 lg:py-32">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <Badge variant="secondary" className="mb-4 px-3 py-1">
                <Zap className="h-3 w-3 mr-1" />
                Your Ultimate Gaming Companion
              </Badge>
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                Organize Your<br />Gaming Universe
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                Track your game collection, monitor progress, discover new titles, and never miss a great deal. 
                The all-in-one platform for serious gamers.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/auth/register">
                  <Button size="lg" className="w-full sm:w-auto">
                    Start Your Collection
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  <Play className="h-4 w-4 mr-2" />
                  Watch Demo
                </Button>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 border-y border-border bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-2xl mx-auto mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Everything You Need to Manage Your Games
              </h2>
              <p className="text-lg text-muted-foreground">
                Powerful features designed to enhance your gaming experience and keep you organized.
              </p>
            </motion.div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={feature.title}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-all duration-300 group cursor-pointer">
                  <CardContent className="p-6">
                    <div className="mb-4">
                      <feature.icon className={`h-12 w-12 ${feature.color} group-hover:scale-110 transition-transform duration-300`} />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                    <p className="text-muted-foreground">{feature.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Social Proof Section */}
      <section className="py-16 bg-muted/50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center max-w-2xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-primary mr-2" />
                <span className="text-sm font-medium text-muted-foreground">
                  Trusted by gamers worldwide
                </span>
              </div>
              <h2 className="text-3xl md:text-4xl font-bold mb-4">
                Join the Gaming Community
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                Thousands of gamers use GameVault to organize their collections, 
                track their progress, and discover amazing games.
              </p>
              <div className="flex items-center justify-center space-x-2 mb-8">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="h-5 w-5 fill-current text-yellow-500" />
                ))}
                <span className="ml-2 text-sm font-medium">4.9/5 from 1,000+ reviews</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 md:py-24">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8 md:p-12 text-center"
          >
            <Shield className="h-12 w-12 text-primary mx-auto mb-6" />
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Level Up Your Gaming?
            </h2>
            <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
              Start organizing your game collection today. It's free to get started, 
              and you'll wonder how you ever managed without it.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/auth/register">
                <Button size="lg" className="w-full sm:w-auto">
                  Create Free Account
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
              <Link to="/auth/login">
                <Button size="lg" variant="outline" className="w-full sm:w-auto">
                  Sign In
                </Button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-border py-8">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <Gamepad2 className="h-6 w-6 text-primary" />
              <span className="font-semibold">GameVault</span>
            </div>
            <div className="text-sm text-muted-foreground">
              © 2024 GameVault. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};