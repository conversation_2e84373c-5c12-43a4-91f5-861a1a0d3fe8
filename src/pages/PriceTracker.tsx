import React from 'react';
import { DollarSign, TrendingDown, TrendingUp, Bell } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { PageContainer } from '@/components/layout/PageContainer';
import { StandardCard } from '@/components/ui/standard-card';
import { ContentGrid } from '@/components/ui/responsive-grid';

export const PriceTracker: React.FC = () => {
  // Mock price tracking data
  const trackedGames = [
    {
      id: '1',
      title: 'Cyberpunk 2077',
      currentPrice: 29.99,
      originalPrice: 59.99,
      targetPrice: 25.00,
      stores: [
        { name: 'Steam', price: 29.99, url: '#' },
        { name: 'Epic Games', price: 27.99, url: '#' },
        { name: 'GOG', price: 29.99, url: '#' }
      ],
      priceHistory: [59.99, 49.99, 39.99, 29.99],
      cover: null
    },
    {
      id: '2',
      title: 'Elden Ring',
      currentPrice: 49.99,
      originalPrice: 59.99,
      targetPrice: 40.00,
      stores: [
        { name: 'Steam', price: 49.99, url: '#' },
        { name: 'Humble Bundle', price: 47.99, url: '#' }
      ],
      priceHistory: [59.99, 59.99, 54.99, 49.99],
      cover: null
    }
  ];

  const getDiscountPercentage = (original: number, current: number) => {
    return Math.round(((original - current) / original) * 100);
  };

  const getProgressToTarget = (current: number, target: number, original: number) => {
    const totalDiscount = original - target;
    const currentDiscount = original - current;
    return Math.min((currentDiscount / totalDiscount) * 100, 100);
  };

  return (
    <PageContainer
      title={
        <div className="flex items-center space-x-2">
          <DollarSign className="h-8 w-8 text-green-500" />
          <span>Price Tracker</span>
        </div>
      }
      description="Monitor game prices and get notified when they hit your target price"
      spacing="md"
    >
      <ContentGrid gap="md" animate={true} staggerChildren={true}>
        {trackedGames.map((game, index) => {
          const discount = getDiscountPercentage(game.originalPrice, game.currentPrice);
          const progress = getProgressToTarget(game.currentPrice, game.targetPrice, game.originalPrice);
          const isAtTarget = game.currentPrice <= game.targetPrice;

          return (
            <StandardCard
              key={game.id}
              size="md"
              hover={true}
              animate={true}
              delay={index * 0.1}
              className="h-full"
            >
              <div className="space-y-4">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">{game.title}</h3>
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-2xl font-bold text-green-600">
                        ${game.currentPrice}
                      </span>
                      {discount > 0 && (
                        <Badge variant="destructive">
                          -{discount}%
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground line-through">
                      Was ${game.originalPrice}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-muted-foreground">Target</p>
                    <p className="text-lg font-semibold">${game.targetPrice}</p>
                    {isAtTarget ? (
                      <Badge className="bg-green-500">
                        Target Reached!
                      </Badge>
                    ) : (
                      <Badge variant="outline">
                        ${(game.currentPrice - game.targetPrice).toFixed(2)} to go
                      </Badge>
                    )}
                  </div>
                </div>
                  {/* Progress to target */}
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progress to target</span>
                      <span>{Math.round(progress)}%</span>
                    </div>
                    <Progress value={progress} className="h-2" />
                  </div>

                  {/* Price trend */}
                  <div className="flex items-center space-x-2">
                    {game.priceHistory[game.priceHistory.length - 1] < game.priceHistory[game.priceHistory.length - 2] ? (
                      <TrendingDown className="h-4 w-4 text-green-500" />
                    ) : (
                      <TrendingUp className="h-4 w-4 text-red-500" />
                    )}
                    <span className="text-sm text-muted-foreground">
                      Price trend over time
                    </span>
                  </div>

                  {/* Stores */}
                  <div>
                    <h4 className="font-medium mb-2">Available at:</h4>
                    <div className="space-y-2">
                      {game.stores.map((store) => (
                        <div key={store.name} className="flex justify-between items-center p-2 bg-muted rounded">
                          <span className="font-medium">{store.name}</span>
                          <div className="flex items-center space-x-2">
                            <span className="font-semibold">${store.price}</span>
                            <Button size="sm" variant="outline" disabled>
                              View Deal
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                {/* Alert setup */}
                <Button className="w-full" variant="outline" disabled>
                  <Bell className="h-4 w-4 mr-2" />
                  Alert Settings (Coming Soon)
                </Button>
              </div>
            </StandardCard>
          );
        })}
      </ContentGrid>

      <StandardCard
        size="md"
        hover={false}
        animate={true}
        delay={0.4}
        className="text-center"
      >
        <DollarSign className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">Advanced price tracking coming soon!</h3>
        <p className="text-muted-foreground">
          We're working on real-time price monitoring across multiple gaming platforms and stores.
        </p>
      </StandardCard>
    </PageContainer>
  );
};