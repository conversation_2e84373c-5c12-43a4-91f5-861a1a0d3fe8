import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, Star, Gamepad2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export const Recommendations: React.FC = () => {
  // Mock recommendation data
  const recommendations = [
    {
      id: '1',
      title: 'The Witcher 3: Wild Hunt',
      reason: 'Similar to games in your collection',
      score: 95,
      genres: ['RPG', 'Adventure'],
      platform: 'PC',
      cover: null
    },
    {
      id: '2',
      title: 'Red Dead Redemption 2',
      reason: 'Trending in Action category',
      score: 92,
      genres: ['Action', 'Adventure'],
      platform: 'PC',
      cover: null
    },
    {
      id: '3',
      title: 'Hades',
      reason: 'High-rated indie game',
      score: 89,
      genres: ['Action', 'Indie'],
      platform: 'PC',
      cover: null
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold flex items-center space-x-2">
          <TrendingUp className="h-8 w-8 text-blue-500" />
          <span>Recommendations</span>
        </h1>
        <p className="text-muted-foreground">
          Discover new games based on your collection and preferences
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.map((game, index) => (
          <motion.div
            key={game.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4, delay: index * 0.1 }}
          >
            <Card className="hover:shadow-lg transition-shadow">
              <div className="relative">
                <div className="aspect-video bg-muted rounded-t-lg flex items-center justify-center">
                  <Gamepad2 className="h-12 w-12 text-muted-foreground" />
                </div>
                <Badge className="absolute top-2 right-2 bg-green-500">
                  {game.score}%
                </Badge>
              </div>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">{game.title}</CardTitle>
                <p className="text-sm text-muted-foreground">{game.reason}</p>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">{game.score}/100</span>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {game.genres.map((genre) => (
                    <Badge key={genre} variant="outline" className="text-xs">
                      {genre}
                    </Badge>
                  ))}
                </div>
                
                <div className="pt-2">
                  <Button className="w-full" disabled>
                    Add to Collection (Coming Soon)
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      <Card>
        <CardContent className="text-center py-8">
          <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">More recommendations coming soon!</h3>
          <p className="text-muted-foreground">
            We're working on AI-powered recommendations based on your gaming history and preferences.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};