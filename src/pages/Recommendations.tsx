import React from 'react';
import { TrendingUp, Star, Gamepad2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { PageContainer } from '@/components/layout/PageContainer';
import { StandardCard } from '@/components/ui/standard-card';
import { ContentGrid } from '@/components/ui/responsive-grid';

export const Recommendations: React.FC = () => {
  // Mock recommendation data
  const recommendations = [
    {
      id: '1',
      title: 'The Witcher 3: Wild Hunt',
      reason: 'Similar to games in your collection',
      score: 95,
      genres: ['RPG', 'Adventure'],
      platform: 'PC',
      cover: null
    },
    {
      id: '2',
      title: 'Red Dead Redemption 2',
      reason: 'Trending in Action category',
      score: 92,
      genres: ['Action', 'Adventure'],
      platform: 'PC',
      cover: null
    },
    {
      id: '3',
      title: 'Hades',
      reason: 'High-rated indie game',
      score: 89,
      genres: ['Action', 'Indie'],
      platform: 'PC',
      cover: null
    }
  ];

  return (
    <PageContainer
      title={
        <div className="flex items-center space-x-2">
          <TrendingUp className="h-8 w-8 text-blue-500" />
          <span>Recommendations</span>
        </div>
      }
      description="Discover new games based on your collection and preferences"
      spacing="md"
    >
      <ContentGrid gap="md" animate={true} staggerChildren={true}>
        {recommendations.map((game, index) => (
          <StandardCard
            key={game.id}
            size="md"
            hover={true}
            animate={true}
            delay={index * 0.1}
            className="h-full"
          >
            <div className="space-y-4">
              <div className="relative">
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                  <Gamepad2 className="h-12 w-12 text-muted-foreground" />
                </div>
                <Badge className="absolute top-2 right-2 bg-green-500">
                  {game.score}%
                </Badge>
              </div>

              <div>
                <h3 className="text-lg font-semibold">{game.title}</h3>
                <p className="text-sm text-muted-foreground">{game.reason}</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm font-medium">{game.score}/100</span>
                </div>

                <div className="flex flex-wrap gap-1">
                  {game.genres.map((genre) => (
                    <Badge key={genre} variant="outline" className="text-xs">
                      {genre}
                    </Badge>
                  ))}
                </div>

                <Button className="w-full" disabled>
                  Add to Collection (Coming Soon)
                </Button>
              </div>
            </div>
          </StandardCard>
        ))}
      </ContentGrid>

      <StandardCard
        size="md"
        hover={false}
        animate={true}
        delay={0.4}
        className="text-center"
      >
        <TrendingUp className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-xl font-semibold mb-2">More recommendations coming soon!</h3>
        <p className="text-muted-foreground">
          We're working on AI-powered recommendations based on your gaming history and preferences.
        </p>
      </StandardCard>
    </PageContainer>
  );
};