import React from 'react';
import { motion } from 'framer-motion';
import { Heart, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useGameStore } from '@/stores/useGameStore';
import { GameCard } from '@/components/games/GameCard';
import { Link } from 'react-router-dom';

export const Wishlist: React.FC = () => {
  const { userGames, updateUserGame, removeFromUserCollection } = useGameStore();
  
  const wishlistGames = userGames.filter(ug => ug.is_wishlist);

  const handleStatusChange = (id: string, status: string) => {
    const updateData = { status: status as any, is_wishlist: false };
    updateUserGame(id, updateData);
    
    // Also update in database
    import('@/lib/supabase').then(({ db }) => {
      db.userGames.updateStatus(id, updateData);
    });
  };

  const handleToggleWishlist = (id: string) => {
    const userGame = userGames.find(ug => ug.id === id);
    if (userGame) {
      const updateData = { is_wishlist: false };
      updateUserGame(id, updateData);
      
      // Also update in database
      import('@/lib/supabase').then(({ db }) => {
        db.userGames.updateStatus(id, updateData);
      });
    }
  };

  const handleEdit = (_userGame: any) => {
    // TODO: Open edit modal
  };

  const handleDelete = (id: string) => {
    removeFromUserCollection(id);
  };

  return (
    <div className="space-y-4 lg:space-y-6">
      <div>
        <h1 className="text-3xl font-bold flex items-center space-x-2">
          <Heart className="h-8 w-8 text-red-500" />
          <span>Wishlist</span>
        </h1>
        <p className="text-muted-foreground">
          Games you want to play ({wishlistGames.length} games)
        </p>
      </div>

      {wishlistGames.length > 0 ? (
        <motion.div 
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 2xl:grid-cols-7 gap-3 lg:gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          {wishlistGames.map((userGame, index) => (
            <motion.div
              key={userGame.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
            >
              <GameCard
                userGame={userGame}
                onStatusChange={handleStatusChange}
                onToggleWishlist={handleToggleWishlist}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </motion.div>
          ))}
        </motion.div>
      ) : (
        <Card className="border-0 shadow-sm bg-card/50 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <Heart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Your wishlist is empty</h3>
            <p className="text-muted-foreground mb-6">
              Start adding games you want to play to your wishlist!
            </p>
            <Button asChild>
              <Link to="/add-game">
                <Plus className="h-4 w-4 mr-2" />
                Add Games
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};