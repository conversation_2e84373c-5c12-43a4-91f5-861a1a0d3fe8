import React from 'react';
import { Heart, Plus } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

import { PageContainer } from '@/components/layout/PageContainer';
import { GameGrid } from '@/components/ui/responsive-grid';
import { useGameStore } from '@/stores/useGameStore';
import { GameCard } from '@/components/games/GameCard';
import { Link } from 'react-router-dom';

export const Wishlist: React.FC = () => {
  const { userGames, updateUserGame, removeFromUserCollection } = useGameStore();
  
  const wishlistGames = userGames.filter(ug => ug.is_wishlist);

  const handleStatusChange = (id: string, status: string) => {
    const updateData = { status: status as any, is_wishlist: false };
    updateUserGame(id, updateData);
    
    // Also update in database
    import('@/lib/supabase').then(({ db }) => {
      db.userGames.updateStatus(id, updateData);
    });
  };

  const handleToggleWishlist = (id: string) => {
    const userGame = userGames.find(ug => ug.id === id);
    if (userGame) {
      const updateData = { is_wishlist: false };
      updateUserGame(id, updateData);
      
      // Also update in database
      import('@/lib/supabase').then(({ db }) => {
        db.userGames.updateStatus(id, updateData);
      });
    }
  };

  const handleEdit = (_userGame: any) => {
    // TODO: Open edit modal
  };

  const handleDelete = (id: string) => {
    removeFromUserCollection(id);
  };

  return (
    <PageContainer
      title={
        <div className="flex items-center space-x-2">
          <Heart className="h-8 w-8 text-red-500" />
          <span>Wishlist</span>
        </div>
      }
      description={`Games you want to play (${wishlistGames.length} games)`}
      spacing="md"
    >
      {wishlistGames.length > 0 ? (
        <GameGrid gap="md" animate={true} staggerChildren={true}>
          {wishlistGames.map((userGame) => (
            <GameCard
              key={userGame.id}
              userGame={userGame}
              onStatusChange={handleStatusChange}
              onToggleWishlist={handleToggleWishlist}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </GameGrid>
      ) : (
        <Card className="border-0 shadow-sm bg-card/50 backdrop-blur-sm">
          <CardContent className="text-center py-12">
            <Heart className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold mb-2">Your wishlist is empty</h3>
            <p className="text-muted-foreground mb-6">
              Start adding games you want to play to your wishlist!
            </p>
            <Button asChild>
              <Link to="/add-game">
                <Plus className="h-4 w-4 mr-2" />
                Add Games
              </Link>
            </Button>
          </CardContent>
        </Card>
      )}
    </PageContainer>
  );
};