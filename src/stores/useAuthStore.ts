import { create } from 'zustand';
import { User, UserProfile, UserPreferences } from '@/types';
import { auth, db } from '@/lib/supabase';

interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  preferences: UserPreferences | null;
  loading: boolean;
  showOnboarding: boolean;
  signIn: (email: string, password: string) => Promise<{ error?: any }>;
  signUp: (email: string, password: string, username?: string) => Promise<{ error?: any }>;
  signOut: () => Promise<void>;
  initialize: () => void;
  setShowOnboarding: (show: boolean) => void;
  completeOnboarding: () => void;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error?: any }>;
  updatePreferences: (updates: Partial<UserPreferences>) => Promise<{ error?: any }>;
  loadUserData: (userId: string) => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  profile: null,
  preferences: null,
  loading: true,
  showOnboarding: false,

  signIn: async (email: string, password: string) => {
    const { data, error } = await auth.signIn(email, password);
    if (data.user) {
      set({ user: data.user as User });
    }
    return { error };
  },

  signUp: async (email: string, password: string, username?: string) => {
    const { data, error } = await auth.signUp(email, password, username);
    if (data.user) {
      set({ user: data.user as User, showOnboarding: true });
    }
    return { error };
  },

  signOut: async () => {
    await auth.signOut();
    set({ user: null, profile: null, preferences: null, showOnboarding: false });
  },

  setShowOnboarding: (show: boolean) => {
    set({ showOnboarding: show });
  },

  completeOnboarding: () => {
    set({ showOnboarding: false });
    // Store in localStorage that user has completed onboarding
    if (get().user?.id) {
      localStorage.setItem(`onboarding-completed-${get().user!.id}`, 'true');
      // Also update in database
      get().updatePreferences({ onboarding_completed: true });
    }
  },

  loadUserData: async (userId: string) => {
    try {
      // Load profile
      const { data: profile, error: profileError } = await db.userProfiles.getProfile(userId);
      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Profile fetch error:', profileError);
      }
      
      // Load preferences
      const { data: preferences, error: preferencesError } = await db.userPreferences.getPreferences(userId);
      if (preferencesError && preferencesError.code !== 'PGRST116') {
        console.error('Preferences fetch error:', preferencesError);
      }
      
      set({ profile, preferences });
      
      // Check onboarding status from database if available, fallback to localStorage
      // If no preferences found, show onboarding for new users
      const shouldShowOnboarding = !preferences || 
        preferences?.onboarding_completed === false || 
        !localStorage.getItem(`onboarding-completed-${userId}`);
      
      set({ showOnboarding: shouldShowOnboarding });
    } catch (error) {
      console.error('Failed to load user data:', error);
      // Set defaults if loading fails
      set({ profile: null, preferences: null, showOnboarding: true });
    }
  },

  updateProfile: async (updates: Partial<UserProfile>) => {
    const { user } = get();
    if (!user?.id) return { error: 'No user logged in' };

    try {
      const { data, error } = await db.userProfiles.updateProfile(user.id, updates);
      if (data) {
        set({ profile: data });
      }
      return { error };
    } catch (error) {
      return { error };
    }
  },

  updatePreferences: async (updates: Partial<UserPreferences>) => {
    const { user } = get();
    if (!user?.id) return { error: 'No user logged in' };

    try {
      const { data, error } = await db.userPreferences.updatePreferences(user.id, updates);
      if (data) {
        set({ preferences: data });
      }
      return { error };
    } catch (error) {
      return { error };
    }
  },

  initialize: () => {
    auth.getCurrentUser().then(({ data: { user } }) => {
      set({ user: user as User | null, loading: false });
      if (user?.id) {
        // Add a small delay to ensure user is fully initialized
        setTimeout(() => get().loadUserData(user.id), 100);
      }
    });

    auth.onAuthStateChange((event, session) => {
      const user = session?.user as User | null;
      set({ user, loading: false });
      
      if (user?.id && event === 'SIGNED_IN') {
        // Add a small delay to ensure user is fully authenticated
        setTimeout(() => get().loadUserData(user.id), 100);
      } else if (!user) {
        set({ profile: null, preferences: null, showOnboarding: false });
      }
    });
  }
}));