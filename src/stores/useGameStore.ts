import { create } from 'zustand';
import { Game, UserGame, Platform, Genre, GameStatus } from '@/types';
import { db } from '@/lib/supabase';

interface GameFilters {
  platform?: Platform;
  genres: Genre[];
  status?: GameStatus;
  rating?: number;
  searchQuery: string;
}

interface GameState {
  games: Game[];
  userGames: UserGame[];
  loading: boolean;
  error: string | null;
  filters: GameFilters;
  sortBy: 'title' | 'dateAdded' | 'rating' | 'releaseDate';
  sortOrder: 'asc' | 'desc';
  
  setGames: (games: Game[]) => void;
  setUserGames: (userGames: UserGame[]) => void;
  fetchUserGames: (userId: string) => Promise<void>;
  updateFilters: (filters: Partial<GameFilters>) => void;
  updateSort: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  addGame: (game: Game) => void;
  updateGame: (id: string, updates: Partial<Game>) => void;
  deleteGame: (id: string) => void;
  addToUserCollection: (userGame: UserGame) => void;
  updateUserGame: (id: string, updates: Partial<UserGame>) => void;
  removeFromUserCollection: (id: string) => void;
}

export const useGameStore = create<GameState>((set) => ({
  games: [],
  userGames: [],
  loading: false,
  error: null,
  filters: {
    genres: [],
    searchQuery: ''
  },
  sortBy: 'title',
  sortOrder: 'asc',

  setGames: (games) => set({ games }),
  setUserGames: (userGames) => set({ userGames }),

  fetchUserGames: async (userId: string) => {
    set({ loading: true, error: null });
    try {
      const { data, error } = await db.userGames.getUserCollection(userId);
      if (error) throw error;
      
      set({ userGames: data || [], loading: false });
    } catch (error) {
      console.error('Error fetching user games:', error);
      set({ error: 'Failed to fetch games', loading: false });
    }
  },

  updateFilters: (newFilters) => 
    set((state) => ({
      filters: { ...state.filters, ...newFilters }
    })),

  updateSort: (sortBy, sortOrder) => 
    set({ sortBy: sortBy as any, sortOrder }),

  addGame: (game) => 
    set((state) => ({
      games: [...state.games, game]
    })),

  updateGame: (id, updates) =>
    set((state) => ({
      games: state.games.map(game => 
        game.id === id ? { ...game, ...updates } : game
      )
    })),

  deleteGame: (id) =>
    set((state) => ({
      games: state.games.filter(game => game.id !== id),
      userGames: state.userGames.filter(userGame => userGame.game_id !== id)
    })),

  addToUserCollection: (userGame) =>
    set((state) => ({
      userGames: [...state.userGames, userGame]
    })),

  updateUserGame: (id, updates) =>
    set((state) => ({
      userGames: state.userGames.map(userGame =>
        userGame.id === id ? { ...userGame, ...updates } : userGame
      )
    })),

  removeFromUserCollection: async (id) => {
    try {
      const { error } = await db.userGames.removeFromCollection(id);
      if (error) throw error;
      
      set((state) => ({
        userGames: state.userGames.filter(userGame => userGame.id !== id)
      }));
    } catch (error) {
      console.error('Error removing game from collection:', error);
    }
  }
}));