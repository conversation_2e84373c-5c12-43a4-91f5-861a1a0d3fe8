export interface User {
  id: string;
  email: string;
  username?: string;
  display_name?: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
}

export interface UserProfile {
  id: string;
  username?: string;
  display_name?: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
  updated_at: string;
}

export interface UserPreferences {
  id: string;
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  default_platform?: Platform;
  show_completed_games: boolean;
  enable_notifications: boolean;
  auto_backup: boolean;
  onboarding_completed: boolean;
  created_at: string;
  updated_at: string;
}

export type Platform = 
  | 'PC' 
  | 'PlayStation 4' 
  | 'PlayStation 5' 
  | 'PlayStation 3' 
  | 'PlayStation 2' 
  | 'PlayStation'
  | 'Xbox Series X/S' 
  | 'Xbox One' 
  | 'Xbox 360' 
  | 'Xbox'
  | 'Nintendo Switch' 
  | 'Nintendo 3DS' 
  | 'Nintendo DS' 
  | 'Wii U' 
  | 'Wii' 
  | 'GameCube'
  | 'iOS' 
  | 'Android' 
  | 'Steam Deck'
  | 'Mac';

export type GameStatus = 'playing' | 'completed' | 'backlog' | 'wishlist';

export type Genre = 
  | 'Action' | 'Adventure' | 'RPG' | 'Strategy' | 'Sports' 
  | 'Racing' | 'Simulation' | 'Puzzle' | 'Fighting' | 'Shooter'
  | 'Horror' | 'Platformer' | 'Indie';

export interface Game {
  id: string;
  title: string;
  platform: Platform;
  genres: Genre[];
  developer?: string;
  publisher?: string;
  release_date?: string;
  description?: string;
  cover_image?: string;
  screenshots?: string[];
  youtube_links?: string[];
  metacritic_score?: number;
  igdb_id?: string;
  created_at: string;
  updated_at: string;
}

export interface UserGame {
  id: string;
  user_id: string;
  game_id: string;
  status: GameStatus;
  personal_rating?: number;
  personal_notes?: string;
  hours_played?: number;
  date_added: string;
  date_completed?: string;
  is_wishlist: boolean;
  game?: Game;
}

export interface PriceTracking {
  id: string;
  game_id: string;
  shop_name: string;
  shop_url: string;
  price: number;
  currency: string;
  last_updated: string;
  game?: Game;
}

export interface CollectionStats {
  totalGames: number;
  completedGames: number;
  currentlyPlaying: number;
  wishlistCount: number;
  totalHours: number;
  averageRating: number;
  platformBreakdown: Record<Platform, number>;
  genreBreakdown: Record<Genre, number>;
}