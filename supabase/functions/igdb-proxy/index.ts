const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, client-id, content-type, apikey, x-client-info, x-authorization, x-client-id",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
};

Deno.serve(async (req: Request) => {
  if (req.method === "OPTIONS") {
    return new Response("ok", { headers: corsHeaders });
  }

  try {
    const { endpoint, query } = await req.json();
    console.log("Received request for endpoint:", endpoint);

    if (!endpoint) {
      console.error("Endpoint is missing from the request body.");
      return new Response(JSON.stringify({ error: "Endpoint missing from request body" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    const clientId = req.headers.get("X-Client-ID");
    const authorization = req.headers.get("X-Authorization");

    console.log("Client-ID:", clientId);
    console.log("Authorization:", authorization ? "Present" : "Missing");

    if (!clientId || !authorization) {
      console.error("Missing required headers");
      return new Response(JSON.stringify({ error: "Missing Client-ID or Authorization headers" }), {
        status: 400,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      });
    }

    console.log("Making request to IGDB API...");
    const igdbResponse = await fetch(`https://api.igdb.com/v4/${endpoint}`, {
      method: "POST",
      headers: {
        "Client-ID": clientId,
        "Authorization": authorization,
        "Accept": "application/json",
        "Content-Type": "text/plain",
      },
      body: query,
    });

    console.log("IGDB Response status:", igdbResponse.status);
    const responseText = await igdbResponse.text();
    console.log("IGDB Response data length:", responseText.length);
    console.log("IGDB Response data sample:", responseText.substring(0, 200));

    if (!igdbResponse.ok) {
      console.error("IGDB API error:", responseText);
      throw new Error(`IGDB API error: ${igdbResponse.status} - ${responseText}`);
    }

    // Parse the response as JSON to validate it
    let jsonData;
    try {
      jsonData = JSON.parse(responseText);
      console.log("Successfully parsed JSON response");
    } catch (parseError) {
      console.error("Failed to parse IGDB response as JSON:", parseError);
      throw new Error(`Invalid JSON response from IGDB: ${responseText}`);
    }

    return new Response(JSON.stringify(jsonData), {
      status: igdbResponse.status,
      headers: {
        ...corsHeaders,
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Edge function error:", error);
    return new Response(JSON.stringify({ 
      error: error.message,
      details: "Failed to fetch data from IGDB API"
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});