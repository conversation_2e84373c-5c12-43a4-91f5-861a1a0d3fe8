/*
  # Game Collection Database Schema

  1. New Tables
    - `games`
      - `id` (uuid, primary key)
      - `title` (text, not null)
      - `platform` (text, not null) - PC or Xbox 360
      - `genres` (text array)
      - `developer` (text, optional)
      - `publisher` (text, optional)
      - `release_date` (date, optional)
      - `description` (text, optional)
      - `cover_image` (text, optional) - URL to cover image
      - `screenshots` (text array, optional) - URLs to screenshots
      - `youtube_links` (text array, optional) - YouTube video URLs
      - `metacritic_score` (integer, optional)
      - `igdb_id` (text, optional) - IGDB API reference
      - `created_at` (timestamptz)
      - `updated_at` (timestamptz)

    - `user_games`
      - `id` (uuid, primary key)
      - `user_id` (uuid, foreign key to auth.users)
      - `game_id` (uuid, foreign key to games)
      - `status` (text) - playing, completed, backlog, wishlist
      - `personal_rating` (integer, 1-5)
      - `personal_notes` (text, optional)
      - `hours_played` (decimal, optional)
      - `date_added` (timestamptz)
      - `date_completed` (timestamptz, optional)
      - `is_wishlist` (boolean, default false)

    - `price_tracking`
      - `id` (uuid, primary key)
      - `game_id` (uuid, foreign key to games)
      - `shop_name` (text, not null)
      - `shop_url` (text, not null)
      - `price` (decimal, not null)
      - `currency` (text, not null, default 'EUR')
      - `last_updated` (timestamptz)

  2. Security
    - Enable RLS on all tables
    - Add policies for authenticated users to manage their own data
    - Games table is readable by all authenticated users
    - Users can only modify their own user_games entries
*/

-- Create games table
CREATE TABLE IF NOT EXISTS games (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL,
  platform text NOT NULL CHECK (platform IN ('PC', 'Xbox 360')),
  genres text[] DEFAULT '{}',
  developer text,
  publisher text,
  release_date date,
  description text,
  cover_image text,
  screenshots text[] DEFAULT '{}',
  youtube_links text[] DEFAULT '{}',
  metacritic_score integer CHECK (metacritic_score >= 0 AND metacritic_score <= 100),
  igdb_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create user_games table (junction table for user's game collection)
CREATE TABLE IF NOT EXISTS user_games (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  game_id uuid REFERENCES games(id) ON DELETE CASCADE NOT NULL,
  status text NOT NULL DEFAULT 'backlog' CHECK (status IN ('playing', 'completed', 'backlog', 'wishlist')),
  personal_rating integer CHECK (personal_rating >= 1 AND personal_rating <= 5),
  personal_notes text,
  hours_played decimal CHECK (hours_played >= 0),
  date_added timestamptz DEFAULT now(),
  date_completed timestamptz,
  is_wishlist boolean DEFAULT false,
  UNIQUE(user_id, game_id)
);

-- Create price_tracking table
CREATE TABLE IF NOT EXISTS price_tracking (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  game_id uuid REFERENCES games(id) ON DELETE CASCADE NOT NULL,
  shop_name text NOT NULL,
  shop_url text NOT NULL,
  price decimal NOT NULL CHECK (price >= 0),
  currency text NOT NULL DEFAULT 'EUR',
  last_updated timestamptz DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_games_title ON games(title);
CREATE INDEX IF NOT EXISTS idx_games_platform ON games(platform);
CREATE INDEX IF NOT EXISTS idx_games_genres ON games USING GIN(genres);
CREATE INDEX IF NOT EXISTS idx_user_games_user_id ON user_games(user_id);
CREATE INDEX IF NOT EXISTS idx_user_games_game_id ON user_games(game_id);
CREATE INDEX IF NOT EXISTS idx_user_games_status ON user_games(status);
CREATE INDEX IF NOT EXISTS idx_user_games_is_wishlist ON user_games(is_wishlist);
CREATE INDEX IF NOT EXISTS idx_price_tracking_game_id ON price_tracking(game_id);

-- Update updated_at timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for games table
DROP TRIGGER IF EXISTS update_games_updated_at ON games;
CREATE TRIGGER update_games_updated_at
  BEFORE UPDATE ON games
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE games ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_games ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_tracking ENABLE ROW LEVEL SECURITY;

-- Games policies (readable by all authenticated users, writable by authenticated users)
CREATE POLICY "Games are readable by authenticated users"
  ON games
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can create games"
  ON games
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update games"
  ON games
  FOR UPDATE
  TO authenticated
  USING (true);

-- User games policies (users can only see and modify their own entries)
CREATE POLICY "Users can view their own game entries"
  ON user_games
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own game entries"
  ON user_games
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own game entries"
  ON user_games
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own game entries"
  ON user_games
  FOR DELETE
  TO authenticated
  USING (auth.uid() = user_id);

-- Price tracking policies (readable by all authenticated users)
CREATE POLICY "Price tracking is readable by authenticated users"
  ON price_tracking
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Authenticated users can insert price tracking"
  ON price_tracking
  FOR INSERT
  TO authenticated
  WITH CHECK (true);

CREATE POLICY "Authenticated users can update price tracking"
  ON price_tracking
  FOR UPDATE
  TO authenticated
  USING (true);